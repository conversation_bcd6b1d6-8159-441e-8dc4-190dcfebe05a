#!/usr/bin/env python3
"""
RecLastTTS API Test Scripti
"""

import os
import sys
import time
import requests
import tempfile
import threading
from pathlib import Path

# Proje yolunu ekle
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# API server'ı ayrı thread'de çalıştır
def start_api_server():
    """API server'ı başlat"""
    from reclasttts.api.server import run_server
    run_server(host="127.0.0.1", port=8000, reload=False)

def wait_for_server(url="http://127.0.0.1:8000", timeout=60):
    """Server'ın hazır olma<PERSON>ını bekle"""
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                return True
        except:
            pass
        time.sleep(1)
    
    return False

def test_health_endpoint():
    """Health endpoint testi"""
    print("🏥 Health endpoint testi...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/health")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check başarılı")
            print(f"  - Status: {data.get('status')}")
            print(f"  - Device: {data.get('device')}")
            print(f"  - GPU: {data.get('gpu_available')}")
            print(f"  - Model: {data.get('model_status')}")
            return True
        else:
            print(f"❌ Health check başarısız: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check hatası: {e}")
        return False

def test_tts_endpoint():
    """TTS endpoint testi"""
    print("\n🎤 TTS endpoint testi...")
    
    try:
        # Test verisi
        test_data = {
            "text": "RecLastTTS API test başarılı!",
            "language": "tr",
            "speed": 1.0,
            "emotion": "neutral"
        }
        
        print(f"📝 Test metni: {test_data['text']}")
        
        start_time = time.time()
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/tts",
            json=test_data,
            timeout=30
        )
        request_time = time.time() - start_time
        
        if response.status_code == 200:
            # Ses dosyasını kaydet
            output_path = "output/api_test.wav"
            os.makedirs("output", exist_ok=True)
            
            with open(output_path, "wb") as f:
                f.write(response.content)
            
            file_size = os.path.getsize(output_path)
            
            print(f"✅ TTS başarılı ({request_time:.1f}s)")
            print(f"  - Dosya: {output_path}")
            print(f"  - Boyut: {file_size} bytes")
            print(f"  - Content-Type: {response.headers.get('content-type')}")
            
            return True
        else:
            print(f"❌ TTS başarısız: {response.status_code}")
            print(f"  - Hata: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ TTS test hatası: {e}")
        return False

def test_voices_endpoint():
    """Voices endpoint testi"""
    print("\n🎭 Voices endpoint testi...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/voices")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Voices listesi başarılı")
            print(f"  - Toplam ses: {data.get('total_count', 0)}")
            
            voices = data.get('voices', [])
            for voice in voices[:3]:  # İlk 3 sesi göster
                print(f"  - {voice.get('name')}: {voice.get('description')}")
            
            return True
        else:
            print(f"❌ Voices listesi başarısız: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Voices test hatası: {e}")
        return False

def test_clone_endpoint():
    """Clone endpoint testi"""
    print("\n🎭 Clone endpoint testi...")
    
    try:
        # Test ses dosyası oluştur
        import numpy as np
        import soundfile as sf
        
        # Basit sinüs dalgası
        sample_rate = 22050
        duration = 3.0
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio = 0.3 * np.sin(2 * np.pi * 440 * t)
        
        # Geçici dosya oluştur
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            sf.write(temp_file.name, audio, sample_rate)
            temp_audio_path = temp_file.name
        
        try:
            # Clone isteği
            files = {
                'audio': ('test_voice.wav', open(temp_audio_path, 'rb'), 'audio/wav')
            }
            data = {
                'text': 'Bu klonlanmış sesle konuşma testi',
                'language': 'tr'
            }
            
            start_time = time.time()
            response = requests.post(
                "http://127.0.0.1:8000/api/v1/clone",
                files=files,
                data=data,
                timeout=60
            )
            request_time = time.time() - start_time
            
            files['audio'][1].close()  # Dosyayı kapat
            
            if response.status_code == 200:
                # Klonlanmış ses dosyasını kaydet
                output_path = "output/api_clone_test.wav"
                os.makedirs("output", exist_ok=True)
                
                with open(output_path, "wb") as f:
                    f.write(response.content)
                
                file_size = os.path.getsize(output_path)
                
                print(f"✅ Clone başarılı ({request_time:.1f}s)")
                print(f"  - Dosya: {output_path}")
                print(f"  - Boyut: {file_size} bytes")
                
                return True
            else:
                print(f"❌ Clone başarısız: {response.status_code}")
                print(f"  - Hata: {response.text}")
                return False
                
        finally:
            # Geçici dosyayı temizle
            try:
                os.unlink(temp_audio_path)
            except:
                pass
            
    except Exception as e:
        print(f"❌ Clone test hatası: {e}")
        return False

def test_status_endpoint():
    """Status endpoint testi"""
    print("\n📊 Status endpoint testi...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/status")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status başarılı")
            print(f"  - Server: {data.get('server_status')}")
            print(f"  - Model: {'Yüklü' if data.get('model_loaded') else 'Yüklü değil'}")
            print(f"  - Toplam istek: {data.get('total_requests')}")
            print(f"  - Ortalama yanıt: {data.get('average_response_time', 0):.2f}s")
            
            memory = data.get('memory_usage', {})
            print(f"  - RAM: {memory.get('rss_mb', 0):.1f}MB")
            if 'allocated_gb' in memory:
                print(f"  - GPU: {memory.get('allocated_gb', 0):.1f}GB")
            
            return True
        else:
            print(f"❌ Status başarısız: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Status test hatası: {e}")
        return False

def test_model_info_endpoint():
    """Model info endpoint testi"""
    print("\n🤖 Model info endpoint testi...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/model/info")
        
        if response.status_code == 200:
            data = response.json()
            model_info = data.get('model_info', {})
            
            print(f"✅ Model info başarılı")
            print(f"  - Model: {model_info.get('model_name')}")
            print(f"  - Device: {model_info.get('device')}")
            print(f"  - Diller: {model_info.get('supported_languages')}")
            print(f"  - Max metin: {model_info.get('max_text_length')}")
            print(f"  - Sample rate: {model_info.get('sample_rate')}")
            
            return True
        else:
            print(f"❌ Model info başarısız: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Model info test hatası: {e}")
        return False

def main():
    """Ana test fonksiyonu"""
    print("🎙️ RecLastTTS API Test Süreci")
    print("=" * 50)
    
    # API server'ı başlat
    print("🚀 API Server başlatılıyor...")
    server_thread = threading.Thread(target=start_api_server, daemon=True)
    server_thread.start()
    
    # Server'ın hazır olmasını bekle
    print("⏳ Server'ın hazır olması bekleniyor...")
    if not wait_for_server():
        print("❌ Server başlatılamadı")
        return False
    
    print("✅ Server hazır!")
    
    # Test fonksiyonları
    tests = [
        ("Health Check", test_health_endpoint),
        ("TTS Endpoint", test_tts_endpoint),
        ("Voices Endpoint", test_voices_endpoint),
        ("Clone Endpoint", test_clone_endpoint),
        ("Status Endpoint", test_status_endpoint),
        ("Model Info Endpoint", test_model_info_endpoint)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                print(f"✅ {test_name} BAŞARILI")
                passed += 1
            else:
                print(f"❌ {test_name} BAŞARISIZ")
        except Exception as e:
            print(f"❌ {test_name} HATA: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 API Test Sonuçları: {passed}/{total} başarılı")
    
    if passed == total:
        print("🎉 Tüm API testleri başarılı!")
        print("🌐 API Dokümantasyonu: http://127.0.0.1:8000/docs")
        return True
    else:
        print("⚠️  Bazı API testleri başarısız.")
        return False

if __name__ == "__main__":
    success = main()
    
    # Test sonrası biraz bekle
    if success:
        print("\n💡 API server çalışmaya devam ediyor...")
        print("   Durdurmak için Ctrl+C tuşlayın")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Test tamamlandı")
    
    sys.exit(0 if success else 1)
