@echo off
chcp 65001 >nul
title RecLastTTS - API Server
echo.
echo ========================================
echo    RecLastTTS - API Server
echo ========================================
echo.
echo [1/3] API Server baslatiliyor...
echo [2/3] API Endpoint: http://127.0.0.1:8000
echo [3/3] Dokumantasyon: http://127.0.0.1:8000/docs
echo [INFO] Durdurmak icin: Ctrl+C
echo.

REM Python sanal ortamini kontrol et
if exist "venv\Scripts\activate.bat" (
    echo [INFO] Sanal ortam aktiflestirildi
    call venv\Scripts\activate.bat
)

echo [INFO] FastAPI modulleri yukleniyor...
echo [INFO] TTS motoru baslatiliyor...
echo [INFO] Bu islem 10-30 saniye surebilir...
echo.

REM API'yi baslat
python run_api.py --host 127.0.0.1 --port 8000

echo.
echo [DONE] API Server durduruldu.
pause
