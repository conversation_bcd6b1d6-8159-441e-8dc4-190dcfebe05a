@echo off
title RecLastTTS - API Server
echo.
echo ========================================
echo    RecLastTTS - API Server
echo ========================================
echo.
echo 🚀 API Server başlatılıyor...
echo 🌐 API Endpoint: http://127.0.0.1:8000
echo 📚 Dokümantasyon: http://127.0.0.1:8000/docs
echo 🛑 Durdurmak için: Ctrl+C
echo.

REM Python sanal ortamını kontrol et
if exist "venv\Scripts\activate.bat" (
    echo 📦 Sanal ortam aktifleştiriliyor...
    call venv\Scripts\activate.bat
)

REM API'yi başlat
python run_api.py --host 127.0.0.1 --port 8000

echo.
echo ✅ API Server durduruldu.
pause
