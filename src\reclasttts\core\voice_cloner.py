"""
RecLastTTS Ses Klonlama Modülü
"""

import os
import json
import shutil
import tempfile
import numpy as np
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
from datetime import datetime

from .config import get_config
from ..utils.logger import get_logger
from ..utils.audio_utils import AudioProcessor


class VoiceCloner:
    """Ses klonlama sınıfı"""
    
    def __init__(self):
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.audio_processor = AudioProcessor()

        # Ses klonları klasörleri
        self.voice_clones_path = self.config.get_voice_clones_path()  # models/voice_clones
        self.assets_voices_path = os.path.join("assets", "voices")   # assets/voices

        # Klasörleri oluştur
        os.makedirs(self.voice_clones_path, exist_ok=True)
        os.makedirs(self.assets_voices_path, exist_ok=True)

        # <PERSON><PERSON>lanm<PERSON><PERSON> sesler veritabanı (assets/voices içinde)
        self.voices_db_path = os.path.join(self.assets_voices_path, "voices.json")
        self.voices_db = self._load_voices_db()
        
        self.logger.info("VoiceCloner başlatıldı")
    
    def _load_voices_db(self) -> Dict[str, Any]:
        """Sesler veritabanını yükle"""
        try:
            if os.path.exists(self.voices_db_path):
                with open(self.voices_db_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {"voices": {}, "created_at": datetime.now().isoformat()}
        except Exception as e:
            self.logger.error(f"Sesler veritabanı yükleme hatası: {e}")
            return {"voices": {}, "created_at": datetime.now().isoformat()}
    
    def _save_voices_db(self) -> None:
        """Sesler veritabanını kaydet"""
        try:
            self.voices_db["updated_at"] = datetime.now().isoformat()
            with open(self.voices_db_path, 'w', encoding='utf-8') as f:
                json.dump(self.voices_db, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Sesler veritabanı kaydetme hatası: {e}")
    
    def create_voice_clone(
        self,
        voice_name: str,
        reference_audio_paths: Union[str, List[str]],
        description: str = "",
        language: str = "tr"
    ) -> bool:
        """
        Yeni ses klonu oluştur
        
        Args:
            voice_name: Ses adı
            reference_audio_paths: Referans ses dosyası(ları)
            description: Açıklama
            language: Dil
            
        Returns:
            Başarı durumu
        """
        try:
            # Ses adını temizle
            voice_name = self._clean_voice_name(voice_name)
            
            if not voice_name:
                self.logger.error("Geçersiz ses adı")
                return False
            
            # Ses zaten var mı kontrol et
            if voice_name in self.voices_db["voices"]:
                self.logger.warning(f"Ses zaten mevcut: {voice_name}")
                return False
            
            # Referans dosyalarını liste yap
            if isinstance(reference_audio_paths, str):
                reference_audio_paths = [reference_audio_paths]
            
            # Referans dosyalarını doğrula
            valid_files = []
            for file_path in reference_audio_paths:
                if self.audio_processor.validate_audio_file(file_path):
                    valid_files.append(file_path)
                else:
                    self.logger.warning(f"Geçersiz ses dosyası: {file_path}")
            
            if not valid_files:
                self.logger.error("Geçerli referans ses dosyası bulunamadı")
                return False
            
            # Ses klasörü oluştur (assets/voices içinde)
            voice_dir = os.path.join(self.assets_voices_path, voice_name)
            os.makedirs(voice_dir, exist_ok=True)
            
            # Referans dosyalarını kopyala ve işle
            processed_files = []
            for i, file_path in enumerate(valid_files):
                # Dosya adını oluştur
                file_ext = Path(file_path).suffix
                new_filename = f"reference_{i+1}{file_ext}"
                new_file_path = os.path.join(voice_dir, new_filename)
                
                # Dosyayı kopyala
                shutil.copy2(file_path, new_file_path)
                
                # Ses işleme
                processed_path = self._process_reference_audio(new_file_path, voice_dir, i+1)
                if processed_path:
                    processed_files.append({
                        "original": new_filename,
                        "processed": os.path.basename(processed_path),
                        "path": processed_path
                    })
                
                self.logger.info(f"Referans dosyası işlendi: {new_filename}")
            
            if not processed_files:
                self.logger.error("Hiç referans dosyası işlenemedi")
                shutil.rmtree(voice_dir, ignore_errors=True)
                return False
            
            # Ses bilgilerini kaydet
            voice_info = {
                "name": voice_name,
                "description": description,
                "language": language,
                "created_at": datetime.now().isoformat(),
                "reference_files": processed_files,
                "usage_count": 0,
                "last_used": None
            }
            
            # Veritabanına ekle
            self.voices_db["voices"][voice_name] = voice_info
            self._save_voices_db()
            
            self.logger.info(f"✅ Ses klonu oluşturuldu: {voice_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Ses klonu oluşturma hatası: {e}")
            return False
    
    def _clean_voice_name(self, name: str) -> str:
        """Ses adını temizle"""
        # Geçersiz karakterleri kaldır
        import re
        cleaned = re.sub(r'[<>:"/\\|?*]', '', name)
        cleaned = cleaned.strip()
        
        # Boş veya çok kısa ise
        if len(cleaned) < 2:
            return ""
        
        return cleaned
    
    def _process_reference_audio(
        self,
        input_path: str,
        output_dir: str,
        index: int
    ) -> Optional[str]:
        """
        Referans ses dosyasını işle
        
        Args:
            input_path: Giriş dosyası
            output_dir: Çıkış klasörü
            index: Dosya indeksi
            
        Returns:
            İşlenmiş dosya yolu
        """
        try:
            # Ses dosyasını yükle
            audio_data = self.audio_processor.load_audio(input_path)
            if audio_data is None:
                return None
            
            # Ses işleme adımları
            
            # 1. Sessizlikleri kırp
            audio_data = self.audio_processor.trim_silence(audio_data, 22050)
            
            # 2. Ses seviyesini normalize et
            audio_data = self.audio_processor.normalize_volume(audio_data, -20.0)
            
            # 3. Örnekleme hızını standartlaştır
            audio_data = self.audio_processor.resample_audio(audio_data, 22050, 22050)
            
            # İşlenmiş dosyayı kaydet
            processed_filename = f"processed_{index}.wav"
            processed_path = os.path.join(output_dir, processed_filename)
            
            success = self.audio_processor.save_audio(
                audio_data, processed_path, 22050, "wav"
            )
            
            if success:
                return processed_path
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Ses işleme hatası: {e}")
            return None
    
    def get_voice_clone_files(self, voice_name: str) -> List[str]:
        """
        Ses klonu dosyalarını al
        
        Args:
            voice_name: Ses adı
            
        Returns:
            Dosya yolları listesi
        """
        try:
            if voice_name not in self.voices_db["voices"]:
                self.logger.error(f"Ses bulunamadı: {voice_name}")
                return []
            
            voice_info = self.voices_db["voices"][voice_name]
            files = []
            
            for ref_file in voice_info["reference_files"]:
                file_path = ref_file["path"]
                if os.path.exists(file_path):
                    files.append(file_path)
                else:
                    self.logger.warning(f"Dosya bulunamadı: {file_path}")
            
            return files
            
        except Exception as e:
            self.logger.error(f"Ses dosyaları alma hatası: {e}")
            return []
    
    def list_voice_clones(self) -> List[Dict[str, Any]]:
        """Tüm ses klonlarını listele"""
        try:
            voices = []
            for voice_name, voice_info in self.voices_db["voices"].items():
                # Dosya sayısını kontrol et
                valid_files = 0
                for ref_file in voice_info["reference_files"]:
                    if os.path.exists(ref_file["path"]):
                        valid_files += 1
                
                voice_summary = {
                    "name": voice_name,
                    "description": voice_info.get("description", ""),
                    "language": voice_info.get("language", "tr"),
                    "created_at": voice_info.get("created_at", ""),
                    "reference_files_count": valid_files,
                    "usage_count": voice_info.get("usage_count", 0),
                    "last_used": voice_info.get("last_used")
                }
                voices.append(voice_summary)
            
            return voices
            
        except Exception as e:
            self.logger.error(f"Ses listesi alma hatası: {e}")
            return []
    
    def delete_voice_clone(self, voice_name: str) -> bool:
        """
        Ses klonunu sil
        
        Args:
            voice_name: Ses adı
            
        Returns:
            Başarı durumu
        """
        try:
            if voice_name not in self.voices_db["voices"]:
                self.logger.error(f"Ses bulunamadı: {voice_name}")
                return False
            
            # Ses klasörünü sil (assets/voices içinden)
            voice_dir = os.path.join(self.assets_voices_path, voice_name)
            if os.path.exists(voice_dir):
                shutil.rmtree(voice_dir)
            
            # Veritabanından kaldır
            del self.voices_db["voices"][voice_name]
            self._save_voices_db()
            
            self.logger.info(f"Ses klonu silindi: {voice_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Ses silme hatası: {e}")
            return False
    
    def update_voice_usage(self, voice_name: str) -> None:
        """Ses kullanım istatistiklerini güncelle"""
        try:
            if voice_name in self.voices_db["voices"]:
                self.voices_db["voices"][voice_name]["usage_count"] += 1
                self.voices_db["voices"][voice_name]["last_used"] = datetime.now().isoformat()
                self._save_voices_db()
        except Exception as e:
            self.logger.error(f"Kullanım güncelleme hatası: {e}")
    
    def get_voice_info(self, voice_name: str) -> Optional[Dict[str, Any]]:
        """
        Ses bilgilerini al
        
        Args:
            voice_name: Ses adı
            
        Returns:
            Ses bilgileri
        """
        try:
            if voice_name not in self.voices_db["voices"]:
                return None
            
            return self.voices_db["voices"][voice_name].copy()
            
        except Exception as e:
            self.logger.error(f"Ses bilgisi alma hatası: {e}")
            return None
    
    def validate_voice_clone(self, voice_name: str) -> bool:
        """
        Ses klonunu doğrula
        
        Args:
            voice_name: Ses adı
            
        Returns:
            Geçerli mi?
        """
        try:
            if voice_name not in self.voices_db["voices"]:
                return False
            
            voice_info = self.voices_db["voices"][voice_name]
            
            # En az bir geçerli dosya olmalı
            valid_files = 0
            for ref_file in voice_info["reference_files"]:
                if os.path.exists(ref_file["path"]):
                    valid_files += 1
            
            return valid_files > 0
            
        except Exception as e:
            self.logger.error(f"Ses doğrulama hatası: {e}")
            return False


# Global instance
_voice_cloner_instance = None

def get_voice_cloner() -> VoiceCloner:
    """Global VoiceCloner instance'ını al"""
    global _voice_cloner_instance
    if _voice_cloner_instance is None:
        _voice_cloner_instance = VoiceCloner()
    return _voice_cloner_instance
