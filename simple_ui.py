#!/usr/bin/env python3
"""
RecLastTTS Basit Web UI
Gradio olmadan basit bir web arayüzü
"""

import os
import sys
import time
import tempfile
from pathlib import Path

# Proje yolunu ekle
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from flask import Flask, render_template_string, request, send_file, jsonify, Response
import json
from reclasttts.core.tts_engine import RecLastTTSEngine
from reclasttts.core.voice_cloner import VoiceCloner

app = Flask(__name__)

# Global değişkenler
tts_engine = None
voice_cloner = None

def get_tts_engine():
    """TTS engine'i al"""
    global tts_engine
    if tts_engine is None:
        print("🚀 TTS Engine başlatılıyor...")
        tts_engine = RecLastTTSEngine()
    return tts_engine

def get_voice_cloner():
    """Voice cloner'ı al"""
    global voice_cloner
    if voice_cloner is None:
        print("🎭 Voice Cloner başlatılıyor...")
        voice_cloner = VoiceCloner()
    return voice_cloner

# HTML Template
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RecLastTTS - Yerel TTS Sistemi</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #eee;
            margin-bottom: 30px;
        }
        
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .audio-player {
            margin-top: 20px;
            width: 100%;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .col {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎙️ RecLastTTS</h1>
            <p>Yerel, Ücretsiz, Sınırsız TTS Sistemi</p>
        </div>
        
        <div class="content">
            <div class="tabs">
                <button class="tab active" onclick="showTab('tts')">🎤 Text to Speech</button>
                <button class="tab" onclick="showTab('clone')">🎭 Ses Klonlama</button>
                <button class="tab" onclick="showTab('status')">📊 Durum</button>
            </div>
            
            <!-- TTS Tab -->
            <div id="tts" class="tab-content active">
                <h2>🎤 Text to Speech</h2>
                <form id="ttsForm">
                    <div class="form-group">
                        <label for="text">Metin:</label>
                        <textarea id="text" name="text" rows="4" placeholder="Buraya çevirmek istediğiniz metni yazın..." required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label for="language">Dil:</label>
                                <select id="language" name="language">
                                    <option value="tr">Türkçe</option>
                                    <option value="en">English</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col">
                            <div class="form-group">
                                <label for="speed">Hız:</label>
                                <select id="speed" name="speed">
                                    <option value="0.5">0.5x (Yavaş)</option>
                                    <option value="0.8">0.8x</option>
                                    <option value="1.0" selected>1.0x (Normal)</option>
                                    <option value="1.2">1.2x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2.0">2.0x (Hızlı)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn">🎵 Ses Üret</button>
                </form>
                
                <div class="loading" id="ttsLoading">
                    <div class="spinner"></div>
                    <p>Ses üretiliyor...</p>
                </div>
                
                <div class="status" id="ttsStatus"></div>
                <audio class="audio-player" id="ttsAudio" controls style="display: none;"></audio>
            </div>
            
            <!-- Clone Tab -->
            <div id="clone" class="tab-content">
                <h2>🎭 Ses Klonlama</h2>
                <form id="cloneForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="cloneText">Metin:</label>
                        <textarea id="cloneText" name="text" rows="3" placeholder="Klonlanmış sesle söylenecek metin..." required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="audioFile">Referans Ses Dosyası:</label>
                        <input type="file" id="audioFile" name="audio" accept="audio/*" required>
                        <small>WAV, MP3, FLAC formatları desteklenir. 3-10 saniye arası temiz ses kayıtları önerilir.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="cloneLanguage">Dil:</label>
                        <select id="cloneLanguage" name="language">
                            <option value="tr">Türkçe</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn">🎭 Ses Klonla</button>
                </form>
                
                <div class="loading" id="cloneLoading">
                    <div class="spinner"></div>
                    <p>Ses klonlanıyor...</p>
                </div>
                
                <div class="status" id="cloneStatus"></div>
                <audio class="audio-player" id="cloneAudio" controls style="display: none;"></audio>
            </div>
            
            <!-- Status Tab -->
            <div id="status" class="tab-content">
                <h2>📊 Sistem Durumu</h2>
                <button class="btn" onclick="refreshStatus()">🔄 Yenile</button>
                <div id="systemStatus" style="margin-top: 20px;">
                    <p>Yüklemek için yenile butonuna tıklayın...</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showTab(tabName) {
            // Tüm sekmeleri gizle
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Tüm tab butonlarını pasif yap
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Seçilen sekmeyi göster
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
        
        function showStatus(elementId, message, isError = false) {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = 'status ' + (isError ? 'error' : 'success');
            statusEl.style.display = 'block';
        }
        
        function hideStatus(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }
        
        function showLoading(elementId) {
            document.getElementById(elementId).style.display = 'block';
        }
        
        function hideLoading(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }
        
        // TTS Form
        document.getElementById('ttsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            hideStatus('ttsStatus');
            showLoading('ttsLoading');
            document.getElementById('ttsAudio').style.display = 'none';
            
            try {
                const response = await fetch('/api/tts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const audioUrl = URL.createObjectURL(blob);
                    
                    const audioEl = document.getElementById('ttsAudio');
                    audioEl.src = audioUrl;
                    audioEl.style.display = 'block';
                    
                    showStatus('ttsStatus', '✅ Ses başarıyla üretildi!');
                } else {
                    const error = await response.text();
                    showStatus('ttsStatus', '❌ Hata: ' + error, true);
                }
            } catch (error) {
                showStatus('ttsStatus', '❌ Bağlantı hatası: ' + error.message, true);
            } finally {
                hideLoading('ttsLoading');
            }
        });
        
        // Clone Form
        document.getElementById('cloneForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            
            hideStatus('cloneStatus');
            showLoading('cloneLoading');
            document.getElementById('cloneAudio').style.display = 'none';
            
            try {
                const response = await fetch('/api/clone', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const audioUrl = URL.createObjectURL(blob);
                    
                    const audioEl = document.getElementById('cloneAudio');
                    audioEl.src = audioUrl;
                    audioEl.style.display = 'block';
                    
                    showStatus('cloneStatus', '✅ Ses başarıyla klonlandı!');
                } else {
                    const error = await response.text();
                    showStatus('cloneStatus', '❌ Hata: ' + error, true);
                }
            } catch (error) {
                showStatus('cloneStatus', '❌ Bağlantı hatası: ' + error.message, true);
            } finally {
                hideLoading('cloneLoading');
            }
        });
        
        // Status refresh
        async function refreshStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                document.getElementById('systemStatus').innerHTML = `
                    <h3>🖥️ Sistem Bilgileri</h3>
                    <p><strong>Durum:</strong> ${data.status}</p>
                    <p><strong>Model:</strong> ${data.model}</p>
                    <p><strong>Device:</strong> ${data.device}</p>
                    <p><strong>GPU:</strong> ${data.gpu ? '✅ Aktif' : '❌ Pasif'}</p>
                    <p><strong>Diller:</strong> ${data.languages.join(', ')}</p>
                    <p><strong>Çalışma Süresi:</strong> ${Math.round(data.uptime)} saniye</p>
                `;
            } catch (error) {
                document.getElementById('systemStatus').innerHTML = `
                    <p style="color: red;">❌ Durum bilgisi alınamadı: ${error.message}</p>
                `;
            }
        }
        
        // Sayfa yüklendiğinde durumu yenile
        window.addEventListener('load', refreshStatus);
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Ana sayfa"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/tts', methods=['POST'])
def api_tts():
    """TTS API endpoint"""
    try:
        data = request.json
        text = data.get('text', '')
        language = data.get('language', 'tr')
        speed = float(data.get('speed', 1.0))
        
        if not text.strip():
            return "Metin boş olamaz", 400
        
        engine = get_tts_engine()
        
        # TTS işlemi
        audio_data = engine.text_to_speech(
            text=text,
            language=language,
            speed=speed
        )
        
        if audio_data is None:
            return "TTS işlemi başarısız", 500
        
        # Geçici dosya oluştur
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_path = temp_file.name
        
        # Ses dosyasını kaydet
        success = engine.save_audio(audio_data, temp_path)
        if not success:
            return "Ses dosyası kaydedilemedi", 500
        
        return send_file(temp_path, mimetype='audio/wav', as_attachment=True, download_name='tts_output.wav')
        
    except Exception as e:
        return f"Hata: {str(e)}", 500

@app.route('/api/clone', methods=['POST'])
def api_clone():
    """Ses klonlama API endpoint"""
    try:
        text = request.form.get('text', '')
        language = request.form.get('language', 'tr')
        audio_file = request.files.get('audio')
        
        if not text.strip():
            return "Metin boş olamaz", 400
        
        if not audio_file:
            return "Ses dosyası gerekli", 400
        
        # Geçici dosya oluştur
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
            audio_file.save(temp_audio.name)
            temp_audio_path = temp_audio.name
        
        try:
            engine = get_tts_engine()
            
            # TTS işlemi
            audio_data = engine.text_to_speech(
                text=text,
                language=language,
                speaker_wav=[temp_audio_path]
            )
            
            if audio_data is None:
                return "Ses klonlama başarısız", 500
            
            # Çıktı dosyası oluştur
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_output:
                output_path = temp_output.name
            
            # Ses dosyasını kaydet
            success = engine.save_audio(audio_data, output_path)
            if not success:
                return "Ses dosyası kaydedilemedi", 500
            
            return send_file(output_path, mimetype='audio/wav', as_attachment=True, download_name='cloned_voice.wav')
            
        finally:
            # Geçici dosyayı temizle
            try:
                os.unlink(temp_audio_path)
            except:
                pass
        
    except Exception as e:
        return f"Hata: {str(e)}", 500

@app.route('/api/status')
def api_status():
    """Durum API endpoint"""
    try:
        engine = get_tts_engine()
        health = engine.health_check()
        
        return jsonify({
            'status': health.get('status', 'unknown'),
            'model': health.get('model_status', 'unknown'),
            'device': health.get('device', 'unknown'),
            'gpu': health.get('gpu_available', False),
            'languages': health.get('supported_languages', []),
            'uptime': time.time() - start_time
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

if __name__ == '__main__':
    start_time = time.time()
    
    print("🎙️ RecLastTTS Basit Web UI")
    print("=" * 40)
    print("🌐 UI: http://127.0.0.1:5000")
    print("📚 Kullanım: Tarayıcınızda yukarıdaki adresi açın")
    print("🛑 Durdurmak için: Ctrl+C")
    print()
    
    app.run(host='127.0.0.1', port=5000, debug=False)
