"""
RecLastTTS Ana Motor
XTTS v2 tabanlı TTS motoru
"""

import os
import torch
import numpy as np
from typing import Optional, Union, List, Dict, Any
from TTS.api import TTS

from .config import get_config
from ..utils.logger import get_logger
from ..utils.audio_utils import AudioProcessor


class RecLastTTSEngine:
    """RecLastTTS ana motor sınıfı"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        TTS motorunu başlat
        
        Args:
            config_path: Konfigürasyon dosyası yolu
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.audio_processor = AudioProcessor()
        
        # TTS modeli
        self.tts_model = None
        self.device = self.config.get_device()

        # Model durumu
        self.is_loaded = False
        self.supported_languages = self.config.model.language_support

        # Varsayılan speaker referansı
        self.default_speaker_path = None

        self.logger.info(f"RecLastTTS Engine başlatılıyor - Device: {self.device}")

        # Modeli yükle
        self._load_model()

        # Varsayılan speaker'ı hazırla
        self._prepare_default_speaker()
    
    def _load_model(self) -> None:
        """XTTS v2 modelini yükle"""
        try:
            self.logger.info("XTTS v2 modeli yükleniyor...")
            
            # Model adını belirle
            model_name = "tts_models/multilingual/multi-dataset/xtts_v2"
            
            # TTS modelini yükle
            self.tts_model = TTS(model_name)
            
            # GPU'ya taşı
            if self.device == "cuda":
                self.tts_model = self.tts_model.to(self.device)
                self.logger.info("Model GPU'ya yüklendi")
            else:
                self.logger.info("Model CPU'da çalışıyor")
            
            self.is_loaded = True
            self.logger.info("✅ XTTS v2 modeli başarıyla yüklendi")
            
        except Exception as e:
            self.logger.error(f"❌ Model yükleme hatası: {e}")
            self.is_loaded = False
            raise

    def _prepare_default_speaker(self) -> None:
        """Varsayılan speaker referansını hazırla"""
        try:
            # Varsayılan speaker dosyası yolu
            default_speaker_dir = os.path.join(self.config.get_temp_path(), "default_speaker")
            os.makedirs(default_speaker_dir, exist_ok=True)

            self.default_speaker_path = os.path.join(default_speaker_dir, "default_speaker.wav")

            # Eğer dosya yoksa, basit bir ses dosyası oluştur
            if not os.path.exists(self.default_speaker_path):
                self._create_default_speaker_audio()

            self.logger.info(f"Varsayılan speaker hazır: {self.default_speaker_path}")

        except Exception as e:
            self.logger.error(f"Varsayılan speaker hazırlama hatası: {e}")
            self.default_speaker_path = None

    def _create_default_speaker_audio(self) -> None:
        """Varsayılan speaker için kaliteli ses dosyası oluştur"""
        try:
            # Önce yerel assets klasöründen ses dosyası kontrol et
            assets_voice_path = os.path.join("assets", "voices", "turkish_speaker.wav")

            if os.path.exists(assets_voice_path):
                # Yerel ses dosyasını kopyala
                import shutil
                shutil.copy2(assets_voice_path, self.default_speaker_path)
                self.logger.info("Yerel varsayılan speaker ses dosyası kopyalandı")
                return

            # Yerel dosya yoksa XTTS v2 örnek sesini indir
            import requests

            # LJSpeech'den kaliteli örnek ses
            sample_url = "https://github.com/coqui-ai/TTS/raw/dev/tests/data/ljspeech/wavs/LJ001-0001.wav"

            try:
                self.logger.info("Kaliteli varsayılan ses indiriliyor...")
                response = requests.get(sample_url, timeout=30)
                response.raise_for_status()

                # Ses dosyasını kaydet
                with open(self.default_speaker_path, 'wb') as f:
                    f.write(response.content)

                self.logger.info("Kaliteli varsayılan speaker ses dosyası indirildi")

            except Exception as download_error:
                self.logger.warning(f"Ses indirme hatası: {download_error}")
                # İndirme başarısız olursa basit ses oluştur
                self._create_simple_speaker_audio()

        except Exception as e:
            self.logger.error(f"Varsayılan speaker ses oluşturma hatası: {e}")
            # Hata durumunda basit ses oluştur
            self._create_simple_speaker_audio()

    def _create_simple_speaker_audio(self) -> None:
        """Basit varsayılan speaker ses dosyası oluştur (fallback)"""
        try:
            import soundfile as sf

            # Basit ama daha iyi ses oluştur
            sample_rate = 22050
            duration = 3.0

            # Beyaz gürültü tabanlı ses (daha doğal)
            t = np.linspace(0, duration, int(sample_rate * duration))
            audio = np.random.normal(0, 0.1, len(t))

            # Düşük geçiren filtre uygula (konuşma benzeri)
            from scipy import signal
            b, a = signal.butter(4, 0.1, 'low')
            audio = signal.filtfilt(b, a, audio)

            # Normalize et
            audio = audio / np.max(np.abs(audio)) * 0.5

            # Ses dosyasını kaydet
            sf.write(self.default_speaker_path, audio, sample_rate)

            self.logger.info("Basit varsayılan speaker ses dosyası oluşturuldu")

        except Exception as e:
            self.logger.error(f"Basit speaker ses oluşturma hatası: {e}")
            self.default_speaker_path = None
    
    def text_to_speech(
        self,
        text: str,
        language: str = "tr",
        speaker_wav: Optional[Union[str, List[str]]] = None,
        output_path: Optional[str] = None,
        speed: float = 1.0,
        emotion: str = "neutral"
    ) -> Optional[np.ndarray]:
        """
        Metni sese çevir
        
        Args:
            text: Çevrilecek metin
            language: Dil kodu (tr, en)
            speaker_wav: Referans ses dosyası(ları)
            output_path: Çıktı dosyası yolu
            speed: Konuşma hızı (0.5-2.0)
            emotion: Duygu (neutral, happy, sad, angry)
            
        Returns:
            Ses verisi (numpy array) veya None
        """
        if not self.is_loaded:
            self.logger.error("Model yüklenmemiş!")
            return None
        
        if not text.strip():
            self.logger.warning("Boş metin!")
            return None
        
        # Dil kontrolü
        if language not in self.supported_languages:
            self.logger.warning(f"Desteklenmeyen dil: {language}, varsayılan 'tr' kullanılıyor")
            language = "tr"
        
        # Metin uzunluğu kontrolü
        if len(text) > self.config.model.max_text_length:
            self.logger.warning(f"Metin çok uzun ({len(text)} karakter), kısaltılıyor")
            text = text[:self.config.model.max_text_length]
        
        try:
            self.logger.info(f"TTS işlemi başlıyor - Dil: {language}, Uzunluk: {len(text)}")
            
            # TTS parametreleri
            tts_kwargs = {
                "text": text,
                "language": language
            }

            # Ses klonlama varsa ekle
            if speaker_wav:
                if isinstance(speaker_wav, str):
                    speaker_wav = [speaker_wav]
                tts_kwargs["speaker_wav"] = speaker_wav
                self.logger.info(f"Ses klonlama kullanılıyor: {len(speaker_wav)} dosya")
            else:
                # Varsayılan speaker kullan (XTTS v2 için gerekli)
                if self.default_speaker_path and os.path.exists(self.default_speaker_path):
                    tts_kwargs["speaker_wav"] = self.default_speaker_path
                    self.logger.info("Varsayılan speaker kullanılıyor")
                else:
                    # Varsayılan speaker yoksa model kendi varsayılan sesini kullanacak
                    self.logger.warning("Varsayılan speaker bulunamadı, model varsayılan sesi kullanılacak")
                    # speaker_wav parametresini ekleme, model kendi varsayılanını kullanacak
            
            # Ses üret
            if output_path:
                # Dosyaya kaydet
                self.tts_model.tts_to_file(file_path=output_path, **tts_kwargs)
                self.logger.info(f"Ses dosyası kaydedildi: {output_path}")
                
                # Dosyayı oku ve döndür
                audio_data = self.audio_processor.load_audio(output_path)
                return audio_data
            else:
                # Sadece veri döndür
                audio_data = self.tts_model.tts(**tts_kwargs)

                if audio_data is not None:
                    # Numpy array'e çevir
                    if isinstance(audio_data, torch.Tensor):
                        audio_data = audio_data.cpu().numpy()
                    elif isinstance(audio_data, list):
                        # Liste ise numpy array'e çevir
                        audio_data = np.array(audio_data, dtype=np.float32)

                    # Veri tipini kontrol et
                    if not isinstance(audio_data, np.ndarray):
                        audio_data = np.array(audio_data, dtype=np.float32)

                    self.logger.info("TTS işlemi başarılı")
                    return audio_data
                else:
                    self.logger.error("TTS işlemi başarısız - boş veri")
                    return None
                    
        except Exception as e:
            self.logger.error(f"TTS hatası: {e}")
            return None
    
    def save_audio(
        self,
        audio_data: np.ndarray,
        output_path: str,
        sample_rate: Optional[int] = None
    ) -> bool:
        """
        Ses verisini dosyaya kaydet
        
        Args:
            audio_data: Ses verisi
            output_path: Çıktı dosyası yolu
            sample_rate: Örnekleme hızı
            
        Returns:
            Başarı durumu
        """
        try:
            if sample_rate is None:
                sample_rate = self.config.audio.sample_rate
            
            success = self.audio_processor.save_audio(
                audio_data, output_path, sample_rate
            )
            
            if success:
                self.logger.info(f"Ses dosyası kaydedildi: {output_path}")
            else:
                self.logger.error(f"Ses dosyası kaydedilemedi: {output_path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Ses kaydetme hatası: {e}")
            return False
    
    def get_supported_languages(self) -> List[str]:
        """Desteklenen dilleri al"""
        return self.supported_languages.copy()
    
    def get_model_info(self) -> Dict[str, Any]:
        """Model bilgilerini al"""
        return {
            "model_name": self.config.model.name,
            "device": self.device,
            "is_loaded": self.is_loaded,
            "supported_languages": self.supported_languages,
            "max_text_length": self.config.model.max_text_length,
            "sample_rate": self.config.audio.sample_rate
        }
    
    def health_check(self) -> Dict[str, Any]:
        """Sistem durumu kontrolü"""
        try:
            # GPU kontrolü
            gpu_available = False
            gpu_memory = 0
            if torch.cuda.is_available():
                gpu_available = True
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            
            # Model kontrolü
            model_status = "loaded" if self.is_loaded else "not_loaded"
            
            # Test TTS
            test_success = False
            try:
                test_audio = self.text_to_speech("Test", language="tr")
                test_success = test_audio is not None and len(test_audio) > 0
            except:
                pass
            
            return {
                "status": "healthy" if (self.is_loaded and test_success) else "unhealthy",
                "model_status": model_status,
                "device": self.device,
                "gpu_available": gpu_available,
                "gpu_memory_gb": round(gpu_memory, 1),
                "supported_languages": self.supported_languages,
                "test_tts": test_success
            }
            
        except Exception as e:
            self.logger.error(f"Health check hatası: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def reload_model(self) -> bool:
        """Modeli yeniden yükle"""
        try:
            self.logger.info("Model yeniden yükleniyor...")
            
            # Mevcut modeli temizle
            if self.tts_model:
                del self.tts_model
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            
            self.is_loaded = False
            
            # Yeniden yükle
            self._load_model()
            
            return self.is_loaded
            
        except Exception as e:
            self.logger.error(f"Model yeniden yükleme hatası: {e}")
            return False
    
    def cleanup(self) -> None:
        """Kaynakları temizle"""
        try:
            if self.tts_model:
                del self.tts_model
            
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            self.is_loaded = False
            self.logger.info("Kaynaklar temizlendi")
            
        except Exception as e:
            self.logger.error(f"Temizleme hatası: {e}")
    
    def __del__(self):
        """Destructor"""
        self.cleanup()
