"""
RecLastTTS UI Bileşenleri
"""

import os
import time
import tempfile
import gradio as gr
from typing import Optional, List, Tuple, Any

from ..core.tts_engine import RecLastTTSEngine
from ..core.voice_cloner import VoiceCloner
from ..core.config import get_config
from ..utils.logger import get_logger


class TTSInterface:
    """TTS arayüzü bileşeni"""

    def __init__(self):
        self.engine = None
        self.logger = get_logger(__name__)
        self.config = get_config()

    def get_engine(self) -> RecLastTTSEngine:
        """TTS engine'i al"""
        if self.engine is None:
            self.logger.info("TTS Engine başlatılıyor...")
            self.engine = RecLastTTSEngine()
        return self.engine

    def text_to_speech(
        self,
        text: str,
        language: str,
        voice: str,
        speed: float,
        progress=gr.Progress()
    ) -> Tuple[Optional[str], str]:
        """
        <PERSON>ni sese çevir

        Returns:
            (audio_file_path, status_message)
        """
        try:
            if not text.strip():
                return None, "❌ Lütfen bir metin girin"

            progress(0.1, desc="TTS Engine hazırlanıyor...")
            engine = self.get_engine()

            progress(0.3, desc="Ses üretiliyor...")

            # Ses klonu seçimi
            speaker_wav = None
            if voice and voice != "Varsayılan":
                cloner = VoiceCloner()
                speaker_files = cloner.get_voice_clone_files(voice)
                if speaker_files:
                    speaker_wav = speaker_files
                    cloner.update_voice_usage(voice)

            # TTS işlemi
            start_time = time.time()
            audio_data = engine.text_to_speech(
                text=text,
                language=language,
                speaker_wav=speaker_wav,
                speed=speed
            )

            if audio_data is None:
                return None, "❌ TTS işlemi başarısız"

            progress(0.8, desc="Ses dosyası kaydediliyor...")

            # Geçici dosya oluştur
            output_dir = self.config.get_output_path()
            os.makedirs(output_dir, exist_ok=True)

            timestamp = int(time.time())
            output_filename = f"tts_output_{timestamp}.wav"
            output_path = os.path.join(output_dir, output_filename)

            # Ses dosyasını kaydet
            success = engine.save_audio(audio_data, output_path)

            if not success:
                return None, "❌ Ses dosyası kaydedilemedi"

            processing_time = time.time() - start_time
            audio_duration = len(audio_data) / 22050

            progress(1.0, desc="Tamamlandı!")

            status = f"✅ Başarılı! İşlem süresi: {processing_time:.1f}s, Ses süresi: {audio_duration:.1f}s"

            return output_path, status

        except Exception as e:
            self.logger.error(f"TTS UI hatası: {e}")
            return None, f"❌ Hata: {str(e)}"

    def create_interface(self) -> gr.Interface:
        """TTS arayüzünü oluştur"""

        # Ses klonları listesi
        def get_voice_options():
            try:
                cloner = VoiceCloner()
                voices = cloner.list_voice_clones()
                voice_names = ["Varsayılan"] + [voice["name"] for voice in voices]
                return voice_names
            except:
                return ["Varsayılan"]

        with gr.Blocks(title="RecLastTTS - Text to Speech") as interface:
            gr.Markdown("# 🎤 Text to Speech")
            gr.Markdown("Metninizi girin ve ses dosyası oluşturun")

            with gr.Row():
                with gr.Column(scale=2):
                    text_input = gr.Textbox(
                        label="Metin",
                        placeholder="Buraya çevirmek istediğiniz metni yazın...",
                        lines=5,
                        max_lines=10
                    )

                    with gr.Row():
                        language_dropdown = gr.Dropdown(
                            choices=["tr", "en"],
                            value="tr",
                            label="Dil"
                        )

                        voice_dropdown = gr.Dropdown(
                            choices=get_voice_options(),
                            value="Varsayılan",
                            label="Ses Klonu"
                        )

                    speed_slider = gr.Slider(
                        minimum=0.5,
                        maximum=2.0,
                        value=1.0,
                        step=0.1,
                        label="Konuşma Hızı"
                    )

                    generate_btn = gr.Button("🎵 Ses Üret", variant="primary", size="lg")

                with gr.Column(scale=1):
                    audio_output = gr.Audio(
                        label="Üretilen Ses",
                        type="filepath"
                    )

                    status_output = gr.Textbox(
                        label="Durum",
                        interactive=False
                    )

            # Event handlers
            generate_btn.click(
                fn=self.text_to_speech,
                inputs=[text_input, language_dropdown, voice_dropdown, speed_slider],
                outputs=[audio_output, status_output],
                show_progress=True
            )

            # Ses klonları listesini yenile
            voice_dropdown.change(
                fn=lambda: gr.Dropdown(choices=get_voice_options()),
                outputs=voice_dropdown
            )

        return interface


class VoiceCloneInterface:
    """Ses klonlama arayüzü bileşeni"""

    def __init__(self):
        self.cloner = None
        self.logger = get_logger(__name__)

    def get_cloner(self) -> VoiceCloner:
        """Voice cloner'ı al"""
        if self.cloner is None:
            self.logger.info("Voice Cloner başlatılıyor...")
            self.cloner = VoiceCloner()
        return self.cloner

    def create_voice_clone(
        self,
        name: str,
        description: str,
        language: str,
        audio_files: List[str],
        progress=gr.Progress()
    ) -> str:
        """
        Yeni ses klonu oluştur

        Returns:
            status_message
        """
        try:
            if not name.strip():
                return "❌ Lütfen ses adı girin"

            if not audio_files:
                return "❌ Lütfen en az bir ses dosyası yükleyin"

            progress(0.2, desc="Ses dosyaları kontrol ediliyor...")

            cloner = self.get_cloner()

            # Dosya yollarını filtrele (None olanları çıkar)
            valid_files = [f for f in audio_files if f is not None]

            if not valid_files:
                return "❌ Geçerli ses dosyası bulunamadı"

            progress(0.5, desc="Ses klonu oluşturuluyor...")

            success = cloner.create_voice_clone(
                voice_name=name,
                reference_audio_paths=valid_files,
                description=description,
                language=language
            )

            if success:
                progress(1.0, desc="Tamamlandı!")
                return f"✅ Ses klonu '{name}' başarıyla oluşturuldu!"
            else:
                return "❌ Ses klonu oluşturulamadı"

        except Exception as e:
            self.logger.error(f"Ses klonlama UI hatası: {e}")
            return f"❌ Hata: {str(e)}"

    def delete_voice_clone(self, voice_name: str) -> Tuple[str, gr.Dropdown]:
        """Ses klonunu sil"""
        try:
            if not voice_name or voice_name == "Seçiniz":
                return "❌ Lütfen silinecek sesi seçin", gr.Dropdown()

            cloner = self.get_cloner()
            success = cloner.delete_voice_clone(voice_name)

            if success:
                # Güncellenmiş liste
                voices = cloner.list_voice_clones()
                voice_choices = ["Seçiniz"] + [voice["name"] for voice in voices]

                return f"✅ Ses klonu '{voice_name}' silindi", gr.Dropdown(choices=voice_choices, value="Seçiniz")
            else:
                return f"❌ Ses klonu '{voice_name}' silinemedi", gr.Dropdown()

        except Exception as e:
            self.logger.error(f"Ses silme UI hatası: {e}")
            return f"❌ Hata: {str(e)}", gr.Dropdown()

    def list_voice_clones(self) -> str:
        """Ses klonlarını listele"""
        try:
            cloner = self.get_cloner()
            voices = cloner.list_voice_clones()

            if not voices:
                return "📭 Henüz ses klonu oluşturulmamış"

            result = f"📋 **Toplam {len(voices)} ses klonu:**\n\n"

            for voice in voices:
                result += f"**{voice['name']}**\n"
                result += f"- Dil: {voice['language']}\n"
                result += f"- Açıklama: {voice['description'] or 'Yok'}\n"
                result += f"- Dosya sayısı: {voice['reference_files_count']}\n"
                result += f"- Kullanım: {voice['usage_count']} kez\n"
                result += f"- Oluşturulma: {voice['created_at'][:10]}\n\n"

            return result

        except Exception as e:
            self.logger.error(f"Ses listesi UI hatası: {e}")
            return f"❌ Hata: {str(e)}"

    def create_interface(self) -> gr.Interface:
        """Ses klonlama arayüzünü oluştur"""

        def get_voice_choices():
            try:
                cloner = self.get_cloner()
                voices = cloner.list_voice_clones()
                return ["Seçiniz"] + [voice["name"] for voice in voices]
            except:
                return ["Seçiniz"]

        with gr.Blocks(title="RecLastTTS - Ses Klonlama") as interface:
            gr.Markdown("# 🎭 Ses Klonlama")
            gr.Markdown("Kendi sesinizi klonlayın ve özel sesler oluşturun")

            with gr.Tabs():
                with gr.TabItem("🆕 Yeni Ses Oluştur"):
                    with gr.Row():
                        with gr.Column():
                            name_input = gr.Textbox(
                                label="Ses Adı",
                                placeholder="Örn: Benim Sesim",
                                max_lines=1
                            )

                            description_input = gr.Textbox(
                                label="Açıklama (Opsiyonel)",
                                placeholder="Bu ses hakkında kısa açıklama...",
                                lines=2
                            )

                            language_dropdown = gr.Dropdown(
                                choices=["tr", "en"],
                                value="tr",
                                label="Dil"
                            )

                            audio_files = gr.File(
                                label="Referans Ses Dosyaları",
                                file_count="multiple",
                                file_types=["audio"],
                                height=150
                            )

                            gr.Markdown("💡 **İpucu:** En iyi sonuç için 3-10 saniye arası, temiz ses kayıtları kullanın")

                            create_btn = gr.Button("🎭 Ses Klonu Oluştur", variant="primary")

                        with gr.Column():
                            create_status = gr.Textbox(
                                label="Durum",
                                interactive=False,
                                lines=3
                            )

                with gr.TabItem("📋 Mevcut Sesler"):
                    with gr.Row():
                        with gr.Column():
                            refresh_btn = gr.Button("🔄 Listeyi Yenile")

                            voice_list = gr.Markdown(
                                value="🔄 Yenilemek için butona tıklayın"
                            )

                        with gr.Column():
                            delete_dropdown = gr.Dropdown(
                                choices=get_voice_choices(),
                                value="Seçiniz",
                                label="Silinecek Ses"
                            )

                            delete_btn = gr.Button("🗑️ Sil", variant="stop")

                            delete_status = gr.Textbox(
                                label="Silme Durumu",
                                interactive=False
                            )

            # Event handlers
            create_btn.click(
                fn=self.create_voice_clone,
                inputs=[name_input, description_input, language_dropdown, audio_files],
                outputs=create_status,
                show_progress=True
            )

            refresh_btn.click(
                fn=self.list_voice_clones,
                outputs=voice_list
            )

            delete_btn.click(
                fn=self.delete_voice_clone,
                inputs=delete_dropdown,
                outputs=[delete_status, delete_dropdown]
            )

        return interface


class SettingsInterface:
    """Ayarlar arayüzü bileşeni"""

    def __init__(self):
        self.config = get_config()
        self.logger = get_logger(__name__)

    def get_system_info(self) -> str:
        """Sistem bilgilerini al"""
        try:
            import torch
            import psutil

            # GPU bilgileri
            gpu_info = "❌ GPU bulunamadı"
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                allocated = torch.cuda.memory_allocated(0) / (1024**3)
                gpu_info = f"✅ {gpu_name} ({gpu_memory:.1f}GB, {allocated:.1f}GB kullanımda)"

            # RAM bilgileri
            memory = psutil.virtual_memory()
            ram_info = f"💾 RAM: {memory.total / (1024**3):.1f}GB (Kullanım: %{memory.percent})"

            # CPU bilgileri
            cpu_info = f"🖥️ CPU: {psutil.cpu_count()} çekirdek (%{psutil.cpu_percent()} kullanım)"

            # Model bilgileri
            try:
                engine = RecLastTTSEngine()
                model_info = engine.get_model_info()
                model_status = f"🤖 Model: {model_info['model_name']} ({model_info['device']})"
                model_loaded = "✅ Yüklü" if model_info['is_loaded'] else "❌ Yüklü değil"
            except:
                model_status = "🤖 Model: Bilgi alınamadı"
                model_loaded = "❌ Hata"

            info = f"""
## 📊 Sistem Bilgileri

### Donanım
{gpu_info}
{ram_info}
{cpu_info}

### Model Durumu
{model_status}
**Durum:** {model_loaded}
**Desteklenen Diller:** TR, EN
**Maksimum Metin:** 500 karakter

### Konfigürasyon
**Device:** {self.config.model.device}
**Sample Rate:** {self.config.audio.sample_rate}Hz
**GPU Bellek Oranı:** %{int(self.config.gpu.memory_fraction * 100)}
"""

            return info

        except Exception as e:
            self.logger.error(f"Sistem bilgisi hatası: {e}")
            return f"❌ Sistem bilgisi alınamadı: {str(e)}"

    def update_config(
        self,
        device: str,
        gpu_memory_fraction: float,
        sample_rate: int,
        max_text_length: int
    ) -> str:
        """Konfigürasyonu güncelle"""
        try:
            # Konfigürasyonu güncelle
            self.config.model.device = device
            self.config.gpu.memory_fraction = gpu_memory_fraction / 100.0
            self.config.audio.sample_rate = sample_rate
            self.config.model.max_text_length = max_text_length

            # Kaydet
            self.config.save_config()

            return "✅ Konfigürasyon başarıyla güncellendi! Değişikliklerin etkili olması için uygulamayı yeniden başlatın."

        except Exception as e:
            self.logger.error(f"Konfigürasyon güncelleme hatası: {e}")
            return f"❌ Konfigürasyon güncellenemedi: {str(e)}"

    def create_interface(self) -> gr.Interface:
        """Ayarlar arayüzünü oluştur"""

        with gr.Blocks(title="RecLastTTS - Ayarlar") as interface:
            gr.Markdown("# ⚙️ Ayarlar")
            gr.Markdown("Sistem ayarlarını görüntüleyin ve değiştirin")

            with gr.Tabs():
                with gr.TabItem("📊 Sistem Bilgileri"):
                    refresh_info_btn = gr.Button("🔄 Bilgileri Yenile")

                    system_info = gr.Markdown(
                        value=self.get_system_info()
                    )

                with gr.TabItem("⚙️ Konfigürasyon"):
                    with gr.Row():
                        with gr.Column():
                            device_dropdown = gr.Dropdown(
                                choices=["cuda", "cpu"],
                                value=self.config.model.device,
                                label="Cihaz Tipi"
                            )

                            gpu_memory_slider = gr.Slider(
                                minimum=10,
                                maximum=100,
                                value=int(self.config.gpu.memory_fraction * 100),
                                step=10,
                                label="GPU Bellek Kullanımı (%)"
                            )

                            sample_rate_dropdown = gr.Dropdown(
                                choices=[16000, 22050, 44100],
                                value=self.config.audio.sample_rate,
                                label="Örnekleme Hızı (Hz)"
                            )

                            max_text_slider = gr.Slider(
                                minimum=100,
                                maximum=1000,
                                value=self.config.model.max_text_length,
                                step=50,
                                label="Maksimum Metin Uzunluğu"
                            )

                            update_btn = gr.Button("💾 Ayarları Kaydet", variant="primary")

                        with gr.Column():
                            config_status = gr.Textbox(
                                label="Durum",
                                interactive=False,
                                lines=3
                            )

                            gr.Markdown("""
### 💡 Ayar Açıklamaları

**Cihaz Tipi:**
- `cuda`: GPU kullan (önerilen)
- `cpu`: CPU kullan (yavaş)

**GPU Bellek Kullanımı:**
- Düşük değer: Daha az bellek, daha yavaş
- Yüksek değer: Daha fazla bellek, daha hızlı

**Örnekleme Hızı:**
- 16kHz: Düşük kalite, hızlı
- 22kHz: Orta kalite (önerilen)
- 44kHz: Yüksek kalite, yavaş

**Maksimum Metin:**
- Tek seferde işlenebilecek maksimum karakter sayısı
                            """)

            # Event handlers
            refresh_info_btn.click(
                fn=self.get_system_info,
                outputs=system_info
            )

            update_btn.click(
                fn=self.update_config,
                inputs=[device_dropdown, gpu_memory_slider, sample_rate_dropdown, max_text_slider],
                outputs=config_status
            )

        return interface