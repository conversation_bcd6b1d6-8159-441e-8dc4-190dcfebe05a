#!/usr/bin/env python3
"""
RecLastTTS Temel Test Scripti
"""

import os
import sys
import time

# Proje yolunu ekle
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Import testleri"""
    print("🔍 Import testleri...")
    
    try:
        # TTS import
        from TTS.api import TTS
        print("✅ TTS import başarılı")
    except Exception as e:
        print(f"❌ TTS import hatası: {e}")
        return False
    
    try:
        # PyTorch import
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA: {torch.version.cuda}")
            print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  CUDA kullanılamıyor")
    except Exception as e:
        print(f"❌ PyTorch import hatası: {e}")
        return False
    
    try:
        # RecLastTTS import
        from reclasttts.core.config import Config
        from reclasttts.core.tts_engine import RecLastTTSEngine
        from reclasttts.utils.logger import get_logger
        print("✅ RecLastTTS modülleri import başarılı")
    except Exception as e:
        print(f"❌ RecLastTTS import hatası: {e}")
        return False
    
    return True

def test_config():
    """Konfigürasyon testi"""
    print("\n⚙️ Konfigürasyon testi...")
    
    try:
        from reclasttts.core.config import Config
        
        config = Config()
        
        # Konfigürasyon doğrulama
        if config.validate_config():
            print("✅ Konfigürasyon geçerli")
        else:
            print("❌ Konfigürasyon geçersiz")
            return False
        
        # Konfigürasyon kaydetme
        config.save_config()
        print("✅ Konfigürasyon kaydedildi")
        
        return True
        
    except Exception as e:
        print(f"❌ Konfigürasyon test hatası: {e}")
        return False

def test_tts_model():
    """TTS model testi"""
    print("\n🤖 TTS model testi...")
    
    try:
        from TTS.api import TTS
        
        print("📥 XTTS v2 modeli yükleniyor... (Bu işlem biraz zaman alabilir)")
        start_time = time.time()
        
        # Model yükle
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2")
        
        load_time = time.time() - start_time
        print(f"✅ Model yüklendi ({load_time:.1f}s)")
        
        # GPU'ya taşı
        import torch
        if torch.cuda.is_available():
            tts = tts.to("cuda")
            print("✅ Model GPU'ya taşındı")
        
        # Basit TTS testi
        print("🎤 Basit TTS testi...")
        test_text = "RecLastTTS test başarılı!"

        # Varsayılan speaker için basit bir ses dosyası oluştur
        import numpy as np
        import soundfile as sf
        temp_speaker_path = "temp/test_speaker.wav"
        os.makedirs("temp", exist_ok=True)

        # Basit sinüs dalgası
        sample_rate = 22050
        duration = 2.0
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio = 0.3 * np.sin(2 * np.pi * 440 * t)
        sf.write(temp_speaker_path, audio, sample_rate)

        start_time = time.time()
        wav = tts.tts(text=test_text, language="tr", speaker_wav=temp_speaker_path)
        tts_time = time.time() - start_time
        
        if wav is not None and len(wav) > 0:
            print(f"✅ TTS başarılı ({tts_time:.1f}s, {len(wav)} sample)")
            
            # Dosyaya kaydet
            output_path = "output/test_basic.wav"
            os.makedirs("output", exist_ok=True)
            
            tts.tts_to_file(
                text=test_text,
                language="tr",
                file_path=output_path
            )
            print(f"✅ Ses dosyası kaydedildi: {output_path}")
            
            return True
        else:
            print("❌ TTS başarısız - boş sonuç")
            return False
            
    except Exception as e:
        print(f"❌ TTS model test hatası: {e}")
        return False

def test_reclasttts_engine():
    """RecLastTTS engine testi"""
    print("\n🎙️ RecLastTTS Engine testi...")
    
    try:
        from reclasttts.core.tts_engine import RecLastTTSEngine
        
        print("🚀 RecLastTTS Engine başlatılıyor...")
        start_time = time.time()
        
        engine = RecLastTTSEngine()
        
        init_time = time.time() - start_time
        print(f"✅ Engine başlatıldı ({init_time:.1f}s)")
        
        # Health check
        health = engine.health_check()
        print(f"🏥 Health check: {health['status']}")
        
        if health['status'] != 'healthy':
            print("⚠️  Engine sağlıklı değil")
            return False
        
        # Model bilgileri
        model_info = engine.get_model_info()
        print(f"📊 Model: {model_info['model_name']}")
        print(f"📊 Device: {model_info['device']}")
        print(f"📊 Diller: {model_info['supported_languages']}")
        
        # TTS testi
        print("🎤 Engine TTS testi...")
        test_text = "RecLastTTS Engine test başarılı!"
        
        start_time = time.time()
        audio_data = engine.text_to_speech(test_text, language="tr")
        tts_time = time.time() - start_time
        
        if audio_data is not None and len(audio_data) > 0:
            print(f"✅ Engine TTS başarılı ({tts_time:.1f}s)")
            
            # Dosyaya kaydet
            output_path = "output/test_engine.wav"
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                print(f"✅ Engine ses dosyası kaydedildi: {output_path}")
            else:
                print("⚠️  Ses dosyası kaydedilemedi")
            
            return True
        else:
            print("❌ Engine TTS başarısız")
            return False
            
    except Exception as e:
        print(f"❌ RecLastTTS Engine test hatası: {e}")
        return False

def main():
    """Ana test fonksiyonu"""
    print("🎙️ RecLastTTS Temel Test Süreci")
    print("=" * 50)
    
    tests = [
        ("Import Testleri", test_imports),
        ("Konfigürasyon Testi", test_config),
        ("TTS Model Testi", test_tts_model),
        ("RecLastTTS Engine Testi", test_reclasttts_engine)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                print(f"✅ {test_name} BAŞARILI")
                passed += 1
            else:
                print(f"❌ {test_name} BAŞARISIZ")
        except Exception as e:
            print(f"❌ {test_name} HATA: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 Test Sonuçları: {passed}/{total} başarılı")
    
    if passed == total:
        print("🎉 Tüm testler başarılı! RecLastTTS hazır.")
        return True
    else:
        print("⚠️  Bazı testler başarısız. Kontrol gerekli.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
