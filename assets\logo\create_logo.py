#!/usr/bin/env python3
"""
RecLastTTS Logo Oluşturucu
PNG ve ICO formatlarında logo oluşturur
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_logo():
    """RecLastTTS logosu oluştur"""
    
    # Logo boyutları
    sizes = [
        (512, 512),  # PNG için
        (256, 256),  # ICO için
        (128, 128),  # Küçük ICO
        (64, 64),    # Favicon
        (32, 32),    # <PERSON><PERSON><PERSON><PERSON>k favicon
        (16, 16)     # Mini favicon
    ]
    
    # Ana logo oluştur (512x512)
    size = 512
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Gradient arka plan
    for i in range(size):
        # Mavi-mor gradient
        r = int(102 + (118 - 102) * i / size)  # 102 -> 118
        g = int(126 + (75 - 126) * i / size)   # 126 -> 75  
        b = int(234 + (162 - 234) * i / size)  # 234 -> 162
        
        draw.line([(0, i), (size, i)], fill=(r, g, b, 255))
    
    # Mikrofon ikonu çiz
    mic_center_x = size // 2
    mic_center_y = size // 2 - 30
    
    # Mikrofon gövdesi
    mic_width = 80
    mic_height = 120
    mic_x1 = mic_center_x - mic_width // 2
    mic_y1 = mic_center_y - mic_height // 2
    mic_x2 = mic_center_x + mic_width // 2
    mic_y2 = mic_center_y + mic_height // 2
    
    # Mikrofon (beyaz, yuvarlatılmış)
    draw.rounded_rectangle([mic_x1, mic_y1, mic_x2, mic_y2], 
                          radius=20, fill=(255, 255, 255, 255))
    
    # Mikrofon detayları
    # Üst kısım (daha koyu)
    draw.rounded_rectangle([mic_x1 + 10, mic_y1 + 10, mic_x2 - 10, mic_y1 + 40], 
                          radius=10, fill=(200, 200, 200, 255))
    
    # Orta çizgiler
    for i in range(3):
        y = mic_y1 + 50 + i * 20
        draw.rectangle([mic_x1 + 15, y, mic_x2 - 15, y + 8], 
                      fill=(180, 180, 180, 255))
    
    # Mikrofon standı
    stand_width = 120
    stand_height = 20
    stand_x1 = mic_center_x - stand_width // 2
    stand_y1 = mic_y2 + 20
    stand_x2 = mic_center_x + stand_width // 2
    stand_y2 = stand_y1 + stand_height
    
    draw.rounded_rectangle([stand_x1, stand_y1, stand_x2, stand_y2], 
                          radius=10, fill=(255, 255, 255, 255))
    
    # Bağlantı çubuğu
    draw.rectangle([mic_center_x - 5, mic_y2, mic_center_x + 5, stand_y1], 
                  fill=(255, 255, 255, 255))
    
    # Ses dalgaları
    wave_colors = [(255, 255, 255, 180), (255, 255, 255, 120), (255, 255, 255, 80)]
    
    for i, color in enumerate(wave_colors):
        radius = 60 + i * 30
        thickness = 8
        
        # Sol taraf
        draw.arc([mic_center_x - radius - 100, mic_center_y - radius, 
                 mic_center_x - 100, mic_center_y + radius], 
                start=-30, end=30, fill=color, width=thickness)
        
        # Sağ taraf  
        draw.arc([mic_center_x + 100, mic_center_y - radius,
                 mic_center_x + radius + 100, mic_center_y + radius], 
                start=150, end=210, fill=color, width=thickness)
    
    # Metin ekle
    try:
        # Font yükle (sistem fontunu dene)
        font_size = 48
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("calibri.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        # Ana başlık
        text1 = "RecLastTTS"
        text_bbox = draw.textbbox((0, 0), text1, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_x = (size - text_width) // 2
        text_y = mic_y2 + 80
        
        # Gölge efekti
        draw.text((text_x + 2, text_y + 2), text1, font=font, fill=(0, 0, 0, 100))
        draw.text((text_x, text_y), text1, font=font, fill=(255, 255, 255, 255))
        
        # Alt başlık
        font_small = ImageFont.truetype("arial.ttf", 24) if font != ImageFont.load_default() else font
        text2 = "Ses Üretim Sistemi"
        text_bbox2 = draw.textbbox((0, 0), text2, font=font_small)
        text_width2 = text_bbox2[2] - text_bbox2[0]
        text_x2 = (size - text_width2) // 2
        text_y2 = text_y + 60
        
        draw.text((text_x2 + 1, text_y2 + 1), text2, font=font_small, fill=(0, 0, 0, 100))
        draw.text((text_x2, text_y2), text2, font=font_small, fill=(255, 255, 255, 255))
        
    except Exception as e:
        print(f"Font yükleme hatası: {e}")
    
    # PNG olarak kaydet
    img.save('assets/logo/reclasttts_logo.png', 'PNG')
    print("✅ PNG logo oluşturuldu: assets/logo/reclasttts_logo.png")
    
    # Farklı boyutlarda ICO dosyaları oluştur
    ico_images = []
    for size in [256, 128, 64, 32, 16]:
        resized = img.resize((size, size), Image.Resampling.LANCZOS)
        ico_images.append(resized)
    
    # ICO dosyası olarak kaydet
    ico_images[0].save('assets/logo/reclasttts_logo.ico', 'ICO', 
                       sizes=[(img.size[0], img.size[1]) for img in ico_images])
    print("✅ ICO logo oluşturuldu: assets/logo/reclasttts_logo.ico")
    
    # Favicon için küçük boyut
    favicon = img.resize((32, 32), Image.Resampling.LANCZOS)
    favicon.save('assets/logo/favicon.ico', 'ICO')
    print("✅ Favicon oluşturuldu: assets/logo/favicon.ico")
    
    print("\n🎨 Logo dosyaları başarıyla oluşturuldu!")
    print("📁 Konum: assets/logo/")
    print("📄 Dosyalar:")
    print("   - reclasttts_logo.png (512x512)")
    print("   - reclasttts_logo.ico (çoklu boyut)")
    print("   - favicon.ico (32x32)")

if __name__ == "__main__":
    # Klasör oluştur
    os.makedirs('assets/logo', exist_ok=True)
    
    try:
        create_logo()
    except ImportError:
        print("❌ Pillow kütüphanesi gerekli!")
        print("📦 Yüklemek için: pip install Pillow")
    except Exception as e:
        print(f"❌ Logo oluşturma hatası: {e}")
