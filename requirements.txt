# RecLastTTS Ses <PERSON><PERSON> - Gereksinimler
# Python 3.11 için optimize edilmiş

# Core TTS Engine (coqui-tts Python 3.12 uyumlu)
coqui-tts>=0.26.0

# PyTorch ve CUDA desteği (RTX 4060 için)
torch>=2.7.0
torchaudio>=2.7.0
torchvision>=2.7.0

# Audio processing
librosa>=0.10.0
soundfile>=0.12.0
scipy>=1.11.0
numpy>=1.24.0,<2.0.0

# Web UI ve API
flask>=3.0.0
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# Utilities
pydantic>=2.5.0
pyyaml>=6.0.1
requests>=2.31.0
aiofiles>=23.2.0
python-dotenv>=1.0.0

# Audio format support
pydub>=0.25.1
ffmpeg-python>=0.2.0

# Performance monitoring
psutil>=5.9.0
GPUtil>=1.4.0

# Development tools (optional)
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0

# Streaming support
websockets>=12.0

# Voice cloning dependencies
transformers>=4.35.0
tokenizers>=0.15.0

# Additional audio processing
noisereduce>=3.0.0
webrtcvad>=2.0.10

# Configuration management
omegaconf>=2.3.0

# Logging
loguru>=0.7.0

# Progress bars
tqdm>=4.66.0

# File handling
pathlib2>=2.3.7

# Memory optimization
memory-profiler>=0.61.0

# GPU memory management
pynvml>=11.5.0

# Audio visualization (optional)
matplotlib>=3.8.0
seaborn>=0.13.0

# API documentation
fastapi-docs>=0.1.0

# Security
cryptography>=41.0.0

# Caching
diskcache>=5.6.0

# Text processing
regex>=2023.10.0
unidecode>=1.3.0

# Model downloading
huggingface-hub>=0.19.0
gdown>=4.7.0

# System monitoring
nvidia-ml-py>=12.535.0

# Audio effects
pedalboard>=0.8.0

# Real-time audio
pyaudio>=0.2.11

# Voice activity detection
silero-vad>=4.0.0

# Text normalization
num2words>=0.5.12
inflect>=7.0.0

# Language detection
langdetect>=1.0.9

# Performance profiling
line-profiler>=4.1.0

# Configuration validation
cerberus>=1.3.4

# File watching
watchdog>=3.0.0

# Process management
supervisor>=4.2.5

# HTTP client
httpx>=0.25.0

# JSON handling
orjson>=3.9.0

# Date/time handling
python-dateutil>=2.8.0

# System information
distro>=1.8.0
