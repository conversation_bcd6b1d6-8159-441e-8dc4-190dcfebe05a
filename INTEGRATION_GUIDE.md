# 🔗 RecLastTTS Entegrasyon Kılavuzu

Bu kılavuz, RecLastTTS'yi di<PERSON> uygulama ve botlarınızla nasıl entegre edeceğinizi gösterir.

## 📋 İçindekiler

1. [API Entegrasyonu](#api-entegrasyonu)
2. [Python SDK](#python-sdk)
3. [Video Üretim Botu](#video-üretim-botu)
4. [Discord Bot](#discord-bot)
5. [Web Uygulaması](#web-uygulaması)
6. [Streaming Entegrasyonu](#streaming-entegrasyonu)
7. [<PERSON><PERSON> İşleme](#batch-işleme)

## 🚀 API Entegrasyonu

### Temel API Kullanımı

```python
import requests
import json

class RecLastTTSClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def text_to_speech(self, text, language="tr", voice="default"):
        """Metni sese çevir"""
        response = self.session.post(
            f"{self.base_url}/api/v1/tts",
            json={
                "text": text,
                "language": language,
                "voice": voice,
                "speed": 1.0,
                "emotion": "neutral"
            }
        )
        
        if response.status_code == 200:
            return response.content
        else:
            raise Exception(f"TTS Error: {response.text}")
    
    def clone_voice(self, reference_audio, text, language="tr"):
        """Ses klonlama"""
        files = {"audio": open(reference_audio, "rb")}
        data = {
            "text": text,
            "language": language
        }
        
        response = self.session.post(
            f"{self.base_url}/api/v1/clone",
            files=files,
            data=data
        )
        
        if response.status_code == 200:
            return response.content
        else:
            raise Exception(f"Clone Error: {response.text}")
    
    def get_voices(self):
        """Mevcut sesleri listele"""
        response = self.session.get(f"{self.base_url}/api/v1/voices")
        return response.json()
    
    def health_check(self):
        """Sistem durumunu kontrol et"""
        response = self.session.get(f"{self.base_url}/api/v1/health")
        return response.json()

# Kullanım örneği
tts_client = RecLastTTSClient()

# Basit TTS
audio_data = tts_client.text_to_speech("Merhaba dünya!")
with open("output.wav", "wb") as f:
    f.write(audio_data)

# Ses klonlama
cloned_audio = tts_client.clone_voice("reference.wav", "Klonlanmış ses")
with open("cloned.wav", "wb") as f:
    f.write(cloned_audio)
```

### Async API Kullanımı

```python
import aiohttp
import asyncio

class AsyncRecLastTTSClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
    
    async def text_to_speech_async(self, text, language="tr"):
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/tts",
                json={"text": text, "language": language}
            ) as response:
                if response.status == 200:
                    return await response.read()
                else:
                    raise Exception(f"TTS Error: {await response.text()}")

# Kullanım
async def main():
    client = AsyncRecLastTTSClient()
    audio = await client.text_to_speech_async("Async test")
    with open("async_output.wav", "wb") as f:
        f.write(audio)

asyncio.run(main())
```

## 🎬 Video Üretim Botu Entegrasyonu

```python
import os
import tempfile
from moviepy.editor import VideoFileClip, AudioFileClip, CompositeAudioClip

class VideoProductionBot:
    def __init__(self, tts_client):
        self.tts = tts_client
    
    def add_voiceover_to_video(self, video_path, script, output_path, language="tr"):
        """Video'ya seslendirme ekle"""
        
        # TTS ile ses üret
        audio_data = self.tts.text_to_speech(script, language)
        
        # Geçici ses dosyası oluştur
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
            temp_audio.write(audio_data)
            temp_audio_path = temp_audio.name
        
        try:
            # Video ve ses dosyalarını yükle
            video = VideoFileClip(video_path)
            audio = AudioFileClip(temp_audio_path)
            
            # Ses uzunluğuna göre video'yu ayarla
            if audio.duration > video.duration:
                # Ses daha uzunsa video'yu tekrarla
                video = video.loop(duration=audio.duration)
            else:
                # Video daha uzunsa ses'i tekrarla veya sessizlik ekle
                audio = audio.set_duration(video.duration)
            
            # Ses ve video'yu birleştir
            final_video = video.set_audio(audio)
            
            # Çıktıyı kaydet
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac'
            )
            
            return output_path
            
        finally:
            # Geçici dosyayı temizle
            os.unlink(temp_audio_path)
    
    def create_narrated_slideshow(self, images, scripts, output_path, language="tr"):
        """Resimlerden sesli slayt gösterisi oluştur"""
        from moviepy.editor import ImageSequenceClip, concatenate_videoclips
        
        clips = []
        
        for i, (image_path, script) in enumerate(zip(images, scripts)):
            # Her script için ses üret
            audio_data = self.tts.text_to_speech(script, language)
            
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                temp_audio.write(audio_data)
                temp_audio_path = temp_audio.name
            
            try:
                # Resmi video klibine çevir
                audio = AudioFileClip(temp_audio_path)
                image_clip = ImageSequenceClip([image_path], durations=[audio.duration])
                image_clip = image_clip.set_audio(audio)
                
                clips.append(image_clip)
                
            finally:
                os.unlink(temp_audio_path)
        
        # Tüm klipleri birleştir
        final_video = concatenate_videoclips(clips)
        final_video.write_videofile(output_path)
        
        return output_path

# Kullanım örneği
tts_client = RecLastTTSClient()
video_bot = VideoProductionBot(tts_client)

# Video'ya seslendirme ekle
video_bot.add_voiceover_to_video(
    "input_video.mp4",
    "Bu video RecLastTTS ile seslendirilmiştir.",
    "output_video.mp4",
    language="tr"
)

# Sesli slayt gösterisi
images = ["slide1.jpg", "slide2.jpg", "slide3.jpg"]
scripts = [
    "İlk slayt açıklaması",
    "İkinci slayt açıklaması", 
    "Üçüncü slayt açıklaması"
]

video_bot.create_narrated_slideshow(
    images, scripts, "slideshow.mp4", "tr"
)
```

## 🤖 Discord Bot Entegrasyonu

```python
import discord
from discord.ext import commands
import io

class TTSBot(commands.Cog):
    def __init__(self, bot, tts_client):
        self.bot = bot
        self.tts = tts_client
    
    @commands.command(name='tts')
    async def text_to_speech(self, ctx, *, text):
        """Discord'da TTS komutu"""
        try:
            # Uzun metinleri kontrol et
            if len(text) > 500:
                await ctx.send("❌ Metin çok uzun! (Max 500 karakter)")
                return
            
            # TTS ile ses üret
            audio_data = self.tts.text_to_speech(text, language="tr")
            
            # Discord'a ses dosyası gönder
            audio_file = discord.File(
                io.BytesIO(audio_data),
                filename="tts_output.wav"
            )
            
            await ctx.send(
                f"🎙️ **{ctx.author.mention}** için TTS:",
                file=audio_file
            )
            
        except Exception as e:
            await ctx.send(f"❌ TTS Hatası: {str(e)}")
    
    @commands.command(name='clone')
    async def clone_voice(self, ctx, *, text):
        """Ses klonlama komutu"""
        if not ctx.message.attachments:
            await ctx.send("❌ Ses dosyası eklemelisiniz!")
            return
        
        attachment = ctx.message.attachments[0]
        
        if not attachment.filename.endswith(('.wav', '.mp3', '.ogg')):
            await ctx.send("❌ Sadece ses dosyaları desteklenir!")
            return
        
        try:
            # Ses dosyasını indir
            audio_data = await attachment.read()
            
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            # Ses klonlama
            cloned_audio = self.tts.clone_voice(temp_file_path, text, "tr")
            
            # Sonucu gönder
            result_file = discord.File(
                io.BytesIO(cloned_audio),
                filename="cloned_voice.wav"
            )
            
            await ctx.send(
                f"🎭 **{ctx.author.mention}** için klonlanmış ses:",
                file=result_file
            )
            
        except Exception as e:
            await ctx.send(f"❌ Klonlama Hatası: {str(e)}")
        finally:
            if 'temp_file_path' in locals():
                os.unlink(temp_file_path)

# Bot kurulumu
intents = discord.Intents.default()
intents.message_content = True

bot = commands.Bot(command_prefix='!', intents=intents)
tts_client = RecLastTTSClient()

@bot.event
async def on_ready():
    print(f'{bot.user} olarak giriş yapıldı!')
    await bot.add_cog(TTSBot(bot, tts_client))

# Bot'u çalıştır
# bot.run('YOUR_BOT_TOKEN')
```

## 🌐 Web Uygulaması Entegrasyonu

### Flask Örneği

```python
from flask import Flask, request, jsonify, send_file
import io

app = Flask(__name__)
tts_client = RecLastTTSClient()

@app.route('/web-tts', methods=['POST'])
def web_tts():
    data = request.json
    text = data.get('text', '')
    language = data.get('language', 'tr')
    
    if not text:
        return jsonify({'error': 'Text required'}), 400
    
    try:
        audio_data = tts_client.text_to_speech(text, language)
        
        return send_file(
            io.BytesIO(audio_data),
            mimetype='audio/wav',
            as_attachment=True,
            download_name='tts_output.wav'
        )
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/web-clone', methods=['POST'])
def web_clone():
    if 'audio' not in request.files:
        return jsonify({'error': 'Audio file required'}), 400
    
    audio_file = request.files['audio']
    text = request.form.get('text', '')
    language = request.form.get('language', 'tr')
    
    if not text:
        return jsonify({'error': 'Text required'}), 400
    
    try:
        # Geçici dosya kaydet
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            audio_file.save(temp_file.name)
            temp_file_path = temp_file.name
        
        cloned_audio = tts_client.clone_voice(temp_file_path, text, language)
        
        return send_file(
            io.BytesIO(cloned_audio),
            mimetype='audio/wav',
            as_attachment=True,
            download_name='cloned_voice.wav'
        )
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        if 'temp_file_path' in locals():
            os.unlink(temp_file_path)

if __name__ == '__main__':
    app.run(debug=True, port=5000)
```

### JavaScript Frontend

```javascript
class RecLastTTSWebClient {
    constructor(baseUrl = 'http://localhost:5000') {
        this.baseUrl = baseUrl;
    }
    
    async textToSpeech(text, language = 'tr') {
        const response = await fetch(`${this.baseUrl}/web-tts`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text, language })
        });
        
        if (response.ok) {
            const blob = await response.blob();
            return URL.createObjectURL(blob);
        } else {
            throw new Error('TTS failed');
        }
    }
    
    async cloneVoice(audioFile, text, language = 'tr') {
        const formData = new FormData();
        formData.append('audio', audioFile);
        formData.append('text', text);
        formData.append('language', language);
        
        const response = await fetch(`${this.baseUrl}/web-clone`, {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const blob = await response.blob();
            return URL.createObjectURL(blob);
        } else {
            throw new Error('Voice cloning failed');
        }
    }
}

// Kullanım
const ttsClient = new RecLastTTSWebClient();

document.getElementById('tts-button').addEventListener('click', async () => {
    const text = document.getElementById('text-input').value;
    const audioUrl = await ttsClient.textToSpeech(text);
    
    const audio = new Audio(audioUrl);
    audio.play();
});
```

## 📊 Streaming Entegrasyonu

```python
import websocket
import json
import threading

class StreamingTTSClient:
    def __init__(self, ws_url="ws://localhost:8000/ws/stream"):
        self.ws_url = ws_url
        self.ws = None
        self.audio_callback = None
    
    def connect(self, audio_callback):
        self.audio_callback = audio_callback
        self.ws = websocket.WebSocketApp(
            self.ws_url,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        
        # Bağlantıyı ayrı thread'de başlat
        self.ws_thread = threading.Thread(target=self.ws.run_forever)
        self.ws_thread.daemon = True
        self.ws_thread.start()
    
    def stream_text(self, text, language="tr"):
        if self.ws:
            message = {
                "action": "stream_tts",
                "text": text,
                "language": language
            }
            self.ws.send(json.dumps(message))
    
    def on_message(self, ws, message):
        data = json.loads(message)
        
        if data.get("type") == "audio_chunk":
            audio_chunk = data.get("audio_data")
            if self.audio_callback and audio_chunk:
                self.audio_callback(audio_chunk)
    
    def on_error(self, ws, error):
        print(f"WebSocket error: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        print("WebSocket connection closed")

# Kullanım
def play_audio_chunk(audio_data):
    # Ses chunk'ını oynat
    import pyaudio
    import base64
    
    audio_bytes = base64.b64decode(audio_data)
    # PyAudio ile oynat...

streaming_client = StreamingTTSClient()
streaming_client.connect(play_audio_chunk)
streaming_client.stream_text("Bu streaming TTS testi")
```

## 🔄 Batch İşleme

```python
import concurrent.futures
from typing import List, Tuple

class BatchTTSProcessor:
    def __init__(self, tts_client, max_workers=4):
        self.tts = tts_client
        self.max_workers = max_workers
    
    def process_batch(self, texts: List[str], language="tr") -> List[bytes]:
        """Birden fazla metni paralel olarak işle"""
        
        def process_single(text):
            return self.tts.text_to_speech(text, language)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(process_single, text) for text in texts]
            results = []
            
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"Batch processing error: {e}")
                    results.append(None)
        
        return results
    
    def process_batch_with_files(self, text_file_pairs: List[Tuple[str, str]], language="tr"):
        """Metinleri işleyip dosyalara kaydet"""
        
        def process_and_save(text_file_pair):
            text, file_path = text_file_pair
            try:
                audio_data = self.tts.text_to_speech(text, language)
                with open(file_path, "wb") as f:
                    f.write(audio_data)
                return file_path
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                return None
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(process_and_save, pair) for pair in text_file_pairs]
            results = []
            
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                if result:
                    results.append(result)
        
        return results

# Kullanım
tts_client = RecLastTTSClient()
batch_processor = BatchTTSProcessor(tts_client)

# Batch metin işleme
texts = [
    "İlk metin",
    "İkinci metin", 
    "Üçüncü metin"
]

audio_results = batch_processor.process_batch(texts)

# Batch dosya işleme
text_file_pairs = [
    ("İlk metin", "output1.wav"),
    ("İkinci metin", "output2.wav"),
    ("Üçüncü metin", "output3.wav")
]

saved_files = batch_processor.process_batch_with_files(text_file_pairs)
print(f"Kaydedilen dosyalar: {saved_files}")
```

## 🔧 Hata Yönetimi ve Retry Logic

```python
import time
import random
from functools import wraps

def retry_on_failure(max_retries=3, delay=1, backoff=2):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    if retries == max_retries:
                        raise e
                    
                    wait_time = delay * (backoff ** (retries - 1))
                    wait_time += random.uniform(0, 1)  # Jitter
                    time.sleep(wait_time)
            
        return wrapper
    return decorator

class RobustTTSClient(RecLastTTSClient):
    @retry_on_failure(max_retries=3, delay=1, backoff=2)
    def text_to_speech(self, text, language="tr", voice="default"):
        return super().text_to_speech(text, language, voice)
    
    @retry_on_failure(max_retries=3, delay=2, backoff=2)
    def clone_voice(self, reference_audio, text, language="tr"):
        return super().clone_voice(reference_audio, text, language)
    
    def text_to_speech_with_fallback(self, text, language="tr"):
        """Fallback mekanizması ile TTS"""
        try:
            return self.text_to_speech(text, language)
        except Exception as e:
            print(f"Primary TTS failed: {e}")
            
            # Fallback: Basit TTS
            try:
                return self.text_to_speech(text, "en")  # İngilizce dene
            except Exception as e2:
                print(f"Fallback TTS failed: {e2}")
                raise e2

# Kullanım
robust_client = RobustTTSClient()
audio = robust_client.text_to_speech_with_fallback("Test metni")
```

Bu entegrasyon kılavuzu, RecLastTTS'yi farklı uygulama türleriyle nasıl entegre edeceğinizi gösterir. Her örnek, gerçek dünya kullanım senaryolarına uygun olarak tasarlanmıştır.
