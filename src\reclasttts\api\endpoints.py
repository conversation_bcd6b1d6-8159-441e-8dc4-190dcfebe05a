"""
RecLastTTS API Endpoints
"""

import os
import time
import tempfile
import asyncio
from typing import List, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Response
from fastapi.responses import FileResponse, StreamingResponse
import io

from .models import (
    TTSRequest, VoiceCloneRequest, VoiceCreateRequest,
    TTSResponse, VoicesListResponse, HealthResponse, 
    ErrorResponse, StatusResponse, ModelInfoResponse
)
from ..core.tts_engine import RecLastTTSEngine
from ..core.voice_cloner import VoiceCloner
from ..utils.logger import get_logger

# Global değişkenler
tts_engine: Optional[RecLastTTSEngine] = None
voice_cloner: Optional[VoiceCloner] = None
server_start_time = time.time()
request_count = 0
total_response_time = 0.0

router = APIRouter()
logger = get_logger(__name__)


def get_tts_engine() -> RecLastTTSEngine:
    """TTS engine'i al"""
    global tts_engine
    if tts_engine is None:
        logger.info("TTS Engine başlatılıyor...")
        tts_engine = RecLastTTSEngine()
    return tts_engine


def get_voice_cloner() -> VoiceCloner:
    """Voice cloner'ı al"""
    global voice_cloner
    if voice_cloner is None:
        logger.info("Voice Cloner başlatılıyor...")
        voice_cloner = VoiceCloner()
    return voice_cloner


def update_stats(response_time: float):
    """İstatistikleri güncelle"""
    global request_count, total_response_time
    request_count += 1
    total_response_time += response_time


@router.post("/tts", response_model=TTSResponse)
async def text_to_speech(request: TTSRequest):
    """
    Metni sese çevir
    
    - **text**: Çevrilecek metin (1-1000 karakter)
    - **language**: Dil kodu (tr, en)
    - **voice**: Ses klonu adı (opsiyonel)
    - **speed**: Konuşma hızı (0.5-2.0)
    - **emotion**: Duygu (neutral, happy, sad, angry)
    """
    start_time = time.time()
    
    try:
        engine = get_tts_engine()
        
        # Ses klonu varsa dosyalarını al (orijinal dosyaları kullan)
        speaker_wav = None
        if request.voice:
            cloner = get_voice_cloner()
            speaker_files = cloner.get_voice_clone_files(request.voice, use_original=True)
            if speaker_files:
                speaker_wav = speaker_files
                cloner.update_voice_usage(request.voice)
                logger.info(f"Ses klonu kullanılıyor: {request.voice} ({len(speaker_files)} dosya)")
            else:
                raise HTTPException(
                    status_code=404,
                    detail=f"Ses klonu bulunamadı: {request.voice}"
                )
        
        # TTS işlemi
        audio_data = engine.text_to_speech(
            text=request.text,
            language=request.language,
            speaker_wav=speaker_wav,
            speed=request.speed,
            emotion=request.emotion
        )
        
        if audio_data is None:
            raise HTTPException(
                status_code=500,
                detail="TTS işlemi başarısız"
            )
        
        # Output klasörüne kaydet (proje kök dizininden)
        from ..core.config import get_config
        config = get_config()

        # Proje kök dizinini bul
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        output_dir = os.path.join(project_root, "output")
        os.makedirs(output_dir, exist_ok=True)

        timestamp = int(time.time())
        filename = f"api_tts_output_{timestamp}.wav"
        output_path = os.path.join(output_dir, filename)

        # Ses dosyasını kaydet
        success = engine.save_audio(audio_data, output_path)
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Ses dosyası kaydedilemedi"
            )
        
        # İstatistikleri güncelle
        processing_time = time.time() - start_time
        update_stats(processing_time)
        
        # Ses süresini hesapla
        audio_duration = len(audio_data) / 22050  # 22kHz sample rate
        
        # Dosyayı döndür
        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename=filename
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"TTS API hatası: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"İç server hatası: {str(e)}"
        )


@router.post("/clone")
async def clone_voice_tts(
    text: str = Form(..., description="Çevrilecek metin"),
    language: str = Form(default="tr", description="Dil kodu"),
    audio: UploadFile = File(..., description="Referans ses dosyası")
):
    """
    Ses klonlama ile TTS
    
    - **text**: Çevrilecek metin
    - **language**: Dil kodu (tr, en)
    - **audio**: Referans ses dosyası (wav, mp3, flac)
    """
    start_time = time.time()
    
    try:
        # Dosya tipini kontrol et
        if not audio.content_type.startswith('audio/'):
            raise HTTPException(
                status_code=400,
                detail="Geçersiz dosya tipi. Ses dosyası gerekli."
            )
        
        # Geçici dosya oluştur
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
            content = await audio.read()
            temp_audio.write(content)
            temp_audio_path = temp_audio.name
        
        try:
            engine = get_tts_engine()
            
            # TTS işlemi
            audio_data = engine.text_to_speech(
                text=text,
                language=language,
                speaker_wav=[temp_audio_path]
            )
            
            if audio_data is None:
                raise HTTPException(
                    status_code=500,
                    detail="Ses klonlama başarısız"
                )
            
            # Output klasörüne kaydet (proje kök dizininden)
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
            output_dir = os.path.join(project_root, "output")
            os.makedirs(output_dir, exist_ok=True)

            timestamp = int(time.time())
            filename = f"api_cloned_voice_{timestamp}.wav"
            output_path = os.path.join(output_dir, filename)

            # Ses dosyasını kaydet
            success = engine.save_audio(audio_data, output_path)
            if not success:
                raise HTTPException(
                    status_code=500,
                    detail="Ses dosyası kaydedilemedi"
                )

            # İstatistikleri güncelle
            processing_time = time.time() - start_time
            update_stats(processing_time)

            # Dosyayı döndür
            return FileResponse(
                output_path,
                media_type="audio/wav",
                filename=filename
            )
            
        finally:
            # Geçici dosyayı temizle
            try:
                os.unlink(temp_audio_path)
            except:
                pass
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ses klonlama API hatası: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"İç server hatası: {str(e)}"
        )


@router.post("/voices/create")
async def create_voice(
    name: str = Form(..., description="Ses adı"),
    description: str = Form(default="", description="Açıklama"),
    language: str = Form(default="tr", description="Dil kodu"),
    audio_files: List[UploadFile] = File(..., description="Referans ses dosyaları")
):
    """
    Yeni ses klonu oluştur
    
    - **name**: Ses adı
    - **description**: Açıklama
    - **language**: Dil kodu
    - **audio_files**: Referans ses dosyaları (1-5 adet)
    """
    try:
        if len(audio_files) == 0:
            raise HTTPException(
                status_code=400,
                detail="En az bir referans ses dosyası gerekli"
            )
        
        if len(audio_files) > 5:
            raise HTTPException(
                status_code=400,
                detail="Maksimum 5 referans ses dosyası"
            )
        
        cloner = get_voice_cloner()
        
        # Geçici dosyalar oluştur
        temp_files = []
        try:
            for audio_file in audio_files:
                if not audio_file.content_type.startswith('audio/'):
                    raise HTTPException(
                        status_code=400,
                        detail=f"Geçersiz dosya tipi: {audio_file.filename}"
                    )
                
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    content = await audio_file.read()
                    temp_file.write(content)
                    temp_files.append(temp_file.name)
            
            # Ses klonu oluştur
            success = cloner.create_voice_clone(
                voice_name=name,
                reference_audio_paths=temp_files,
                description=description,
                language=language
            )
            
            if success:
                return {"success": True, "message": f"Ses klonu oluşturuldu: {name}"}
            else:
                raise HTTPException(
                    status_code=500,
                    detail="Ses klonu oluşturulamadı"
                )
                
        finally:
            # Geçici dosyaları temizle
            for temp_file in temp_files:
                try:
                    os.unlink(temp_file)
                except:
                    pass
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ses oluşturma API hatası: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"İç server hatası: {str(e)}"
        )


@router.get("/voices", response_model=VoicesListResponse)
async def list_voices():
    """Tüm ses klonlarını listele"""
    try:
        cloner = get_voice_cloner()
        voices = cloner.list_voice_clones()
        
        return VoicesListResponse(
            success=True,
            voices=voices,
            total_count=len(voices)
        )
        
    except Exception as e:
        logger.error(f"Ses listesi API hatası: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"İç server hatası: {str(e)}"
        )


@router.delete("/voices/{voice_name}")
async def delete_voice(voice_name: str):
    """Ses klonunu sil"""
    try:
        cloner = get_voice_cloner()
        success = cloner.delete_voice_clone(voice_name)
        
        if success:
            return {"success": True, "message": f"Ses klonu silindi: {voice_name}"}
        else:
            raise HTTPException(
                status_code=404,
                detail=f"Ses klonu bulunamadı: {voice_name}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ses silme API hatası: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"İç server hatası: {str(e)}"
        )


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Sistem durumu kontrolü"""
    try:
        engine = get_tts_engine()
        health_data = engine.health_check()
        
        # Uptime ekle
        health_data["uptime"] = time.time() - server_start_time
        
        return HealthResponse(**health_data)
        
    except Exception as e:
        logger.error(f"Health check API hatası: {e}")
        return HealthResponse(
            status="error",
            model_status="error",
            device="unknown",
            gpu_available=False,
            gpu_memory_gb=0.0,
            supported_languages=[],
            test_tts=False,
            uptime=time.time() - server_start_time
        )


@router.get("/status", response_model=StatusResponse)
async def get_status():
    """Server durumu"""
    try:
        engine = get_tts_engine()
        
        # Bellek kullanımı
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        memory_usage = {
            "rss_mb": memory_info.rss / (1024 * 1024),
            "vms_mb": memory_info.vms / (1024 * 1024),
            "percent": process.memory_percent()
        }
        
        # GPU bellek
        try:
            import torch
            if torch.cuda.is_available():
                gpu_memory = {
                    "allocated_gb": torch.cuda.memory_allocated(0) / (1024**3),
                    "cached_gb": torch.cuda.memory_reserved(0) / (1024**3)
                }
                memory_usage.update(gpu_memory)
        except:
            pass
        
        avg_response_time = total_response_time / request_count if request_count > 0 else 0.0
        
        return StatusResponse(
            success=True,
            server_status="running",
            model_loaded=engine.is_loaded,
            active_requests=0,  # TODO: Implement active request tracking
            total_requests=request_count,
            average_response_time=avg_response_time,
            memory_usage=memory_usage
        )
        
    except Exception as e:
        logger.error(f"Status API hatası: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"İç server hatası: {str(e)}"
        )


@router.get("/model/info", response_model=ModelInfoResponse)
async def get_model_info():
    """Model bilgilerini al"""
    try:
        engine = get_tts_engine()
        model_info = engine.get_model_info()
        
        return ModelInfoResponse(
            success=True,
            model_info=model_info
        )
        
    except Exception as e:
        logger.error(f"Model info API hatası: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"İç server hatası: {str(e)}"
        )
