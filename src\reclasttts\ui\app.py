"""
RecLastTTS Ana UI Uygulaması
"""

import os
import gradio as gr
from typing import Optional

from .components import TTSInterface, VoiceCloneInterface, SettingsInterface
from ..core.config import get_config
from ..utils.logger import get_logger


class RecLastTTSApp:
    """Ana UI uygulaması"""
    
    def __init__(self):
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # Bileşenler
        self.tts_interface = TTSInterface()
        self.voice_clone_interface = VoiceCloneInterface()
        self.settings_interface = SettingsInterface()
        
        self.app = None
    
    def create_header(self) -> gr.HTML:
        """Başlık bölümü"""
        header_html = """
        <div style="text-align: center; padding: 20px; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); border-radius: 10px; margin-bottom: 20px;">
            <h1 style="color: white; margin: 0; font-size: 2.5em;">🎙️ RecLastTTS</h1>
            <p style="color: white; margin: 10px 0 0 0; font-size: 1.2em;"><PERSON><PERSON>, Ücretsiz, Sınırsız TTS Sistemi</p>
            <p style="color: #e0e0e0; margin: 5px 0 0 0;">Ses Klonlama • GPU Hızlandırma • TR/EN Dil Desteği</p>
        </div>
        """
        return gr.HTML(header_html)
    
    def create_footer(self) -> gr.HTML:
        """Alt bilgi bölümü"""
        footer_html = """
        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 10px; margin-top: 20px; border: 1px solid #e9ecef;">
            <p style="margin: 0; color: #6c757d;">
                <strong>RecLastTTS v1.0.0</strong> | 
                Powered by <a href="https://github.com/coqui-ai/TTS" target="_blank">XTTS v2</a> | 
                <a href="http://localhost:8000/docs" target="_blank">📚 API Docs</a>
            </p>
            <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 0.9em;">
                🎮 GPU: RTX 4060 | 🧠 Model: XTTS v2 | 🌍 Diller: TR, EN
            </p>
        </div>
        """
        return gr.HTML(footer_html)
    
    def create_quick_stats(self) -> gr.HTML:
        """Hızlı istatistikler"""
        try:
            # Ses klonları sayısı
            voice_count = len(self.voice_clone_interface.get_cloner().list_voice_clones())
            
            # Sistem durumu
            engine = self.tts_interface.get_engine()
            health = engine.health_check()
            
            status_color = "#28a745" if health['status'] == 'healthy' else "#dc3545"
            gpu_status = "✅ Aktif" if health['gpu_available'] else "❌ Pasif"
            
            stats_html = f"""
            <div style="display: flex; justify-content: space-around; padding: 15px; background: #f8f9fa; border-radius: 10px; margin: 10px 0;">
                <div style="text-align: center;">
                    <div style="font-size: 1.5em; font-weight: bold; color: {status_color};">●</div>
                    <div style="font-size: 0.9em; color: #6c757d;">Sistem Durumu</div>
                    <div style="font-weight: bold;">{health['status'].title()}</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 1.5em; font-weight: bold; color: #007bff;">🎭</div>
                    <div style="font-size: 0.9em; color: #6c757d;">Ses Klonları</div>
                    <div style="font-weight: bold;">{voice_count}</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 1.5em; font-weight: bold; color: #28a745;">🎮</div>
                    <div style="font-size: 0.9em; color: #6c757d;">GPU</div>
                    <div style="font-weight: bold;">{gpu_status}</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 1.5em; font-weight: bold; color: #6f42c1;">🧠</div>
                    <div style="font-size: 0.9em; color: #6c757d;">Model</div>
                    <div style="font-weight: bold;">XTTS v2</div>
                </div>
            </div>
            """
            
            return gr.HTML(stats_html)
            
        except Exception as e:
            self.logger.error(f"Stats oluşturma hatası: {e}")
            return gr.HTML("<div style='text-align: center; color: #dc3545;'>⚠️ İstatistikler yüklenemedi</div>")
    
    def create_app(self) -> gr.Blocks:
        """Ana uygulamayı oluştur"""
        
        # Gradio tema
        theme = gr.themes.Soft(
            primary_hue="blue",
            secondary_hue="purple",
            neutral_hue="gray"
        )
        
        with gr.Blocks(
            title="RecLastTTS - Yerel TTS Sistemi",
            theme=theme,
            css="""
            .gradio-container {
                max-width: 1200px !important;
                margin: auto !important;
            }
            .tab-nav {
                background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
            }
            """
        ) as app:
            
            # Başlık
            self.create_header()
            
            # Hızlı istatistikler
            self.create_quick_stats()
            
            # Ana sekmeler
            with gr.Tabs():
                with gr.TabItem("🎤 Text to Speech", id="tts"):
                    self.tts_interface.create_interface()
                
                with gr.TabItem("🎭 Ses Klonlama", id="voice_clone"):
                    self.voice_clone_interface.create_interface()
                
                with gr.TabItem("⚙️ Ayarlar", id="settings"):
                    self.settings_interface.create_interface()
                
                with gr.TabItem("📚 Kullanım Kılavuzu", id="help"):
                    self.create_help_tab()
            
            # Alt bilgi
            self.create_footer()
        
        self.app = app
        return app
    
    def create_help_tab(self) -> None:
        """Yardım sekmesi"""
        help_content = """
# 📚 RecLastTTS Kullanım Kılavuzu

## 🎤 Text to Speech Nasıl Kullanılır?

1. **Metin Girin**: Çevirmek istediğiniz metni yazın (max 500 karakter)
2. **Dil Seçin**: Türkçe (tr) veya İngilizce (en)
3. **Ses Seçin**: Varsayılan ses veya oluşturduğunuz ses klonları
4. **Hız Ayarlayın**: 0.5x (yavaş) ile 2.0x (hızlı) arası
5. **Üret Butonuna Tıklayın**: Ses dosyası oluşturulacak

## 🎭 Ses Klonlama Nasıl Yapılır?

### Yeni Ses Oluşturma:
1. **Ses Adı**: Benzersiz bir isim verin
2. **Açıklama**: Kısa bir açıklama yazın (opsiyonel)
3. **Dil**: Sesin hangi dilde olduğunu seçin
4. **Ses Dosyaları**: 1-5 adet referans ses dosyası yükleyin

### 💡 En İyi Sonuç İçin:
- **Ses Kalitesi**: Temiz, gürültüsüz kayıtlar kullanın
- **Süre**: Her dosya 3-10 saniye arası olmalı
- **İçerik**: Farklı kelimeler ve cümleler içeren kayıtlar
- **Format**: WAV, MP3, FLAC formatları desteklenir

## ⚙️ Ayarlar

### Cihaz Tipi:
- **CUDA**: GPU kullanır (önerilen, hızlı)
- **CPU**: İşlemci kullanır (yavaş ama uyumlu)

### GPU Bellek:
- Düşük değer: Az bellek kullanır, yavaş çalışır
- Yüksek değer: Fazla bellek kullanır, hızlı çalışır

## 🔧 API Kullanımı

RecLastTTS'yi diğer uygulamalarınızla entegre etmek için:

```bash
# API Server'ı başlatın
python run_api.py

# API dokümantasyonuna erişin
http://localhost:8000/docs
```

### Basit TTS İsteği:
```python
import requests

response = requests.post("http://localhost:8000/api/v1/tts", json={
    "text": "Merhaba dünya!",
    "language": "tr"
})

with open("output.wav", "wb") as f:
    f.write(response.content)
```

## 🚨 Sorun Giderme

### Yaygın Sorunlar:

**"GPU bulunamadı" hatası:**
- CUDA sürücülerini kontrol edin
- Ayarlardan CPU moduna geçin

**"Model yüklenemedi" hatası:**
- İnternet bağlantınızı kontrol edin
- Uygulamayı yeniden başlatın

**Ses kalitesi düşük:**
- Daha kaliteli referans sesler kullanın
- Örnekleme hızını artırın (Ayarlar)

**Yavaş çalışma:**
- GPU modunu aktif edin
- GPU bellek oranını artırın

## 📞 Destek

Sorunlarınız için:
1. Önce bu kılavuzu kontrol edin
2. Sistem bilgilerini kontrol edin (Ayarlar > Sistem Bilgileri)
3. Log dosyalarını inceleyin (`logs/reclasttts.log`)

## 🎯 İpuçları

- **Performans**: İlk kullanımda model yükleme zaman alabilir
- **Bellek**: Uzun metinleri parçalara bölün
- **Kalite**: Ses klonlama için en az 3 farklı ses dosyası kullanın
- **Hız**: Kısa metinler için daha hızlı sonuç alırsınız

---

**RecLastTTS v1.0.0** - Yerel TTS çözümünüz! 🎙️
        """
        
        gr.Markdown(help_content)


def create_ui_app() -> gr.Blocks:
    """UI uygulamasını oluştur"""
    app_instance = RecLastTTSApp()
    return app_instance.create_app()


def launch_ui(
    host: str = None,
    port: int = None,
    share: bool = False,
    debug: bool = False
) -> None:
    """
    UI'yi başlat
    
    Args:
        host: Host adresi
        port: Port numarası
        share: Gradio share link
        debug: Debug modu
    """
    config = get_config()
    logger = get_logger(__name__)
    
    # Varsayılan değerler
    if host is None:
        host = config.ui.host
    if port is None:
        port = config.ui.port
    
    logger.info(f"🌐 UI başlatılıyor: http://{host}:{port}")
    
    # UI uygulamasını oluştur
    app = create_ui_app()
    
    try:
        # Gradio'yu başlat
        app.launch(
            server_name=host,
            server_port=port,
            share=share,
            debug=debug,
            show_error=True,
            quiet=False
        )
    except KeyboardInterrupt:
        logger.info("🛑 UI durduruldu (Ctrl+C)")
    except Exception as e:
        logger.error(f"❌ UI hatası: {e}")


def main():
    """CLI entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RecLastTTS Web UI")
    parser.add_argument("--host", default=None, help="Host adresi")
    parser.add_argument("--port", type=int, default=None, help="Port numarası")
    parser.add_argument("--share", action="store_true", help="Gradio share link")
    parser.add_argument("--debug", action="store_true", help="Debug modu")
    
    args = parser.parse_args()
    
    launch_ui(
        host=args.host,
        port=args.port,
        share=args.share,
        debug=args.debug
    )


if __name__ == "__main__":
    main()
