"""
RecLastTTS Ana UI Uygulaması - Flask Tabanlı
"""

import os
import time
import tempfile
from flask import Flask, render_template_string, request, send_file, jsonify

from ..core.tts_engine import RecLastTTSEngine
from ..core.voice_cloner import VoiceCloner
from ..core.config import get_config
from ..utils.logger import get_logger


# Global değişkenler
tts_engine = None
voice_cloner = None
start_time = time.time()


def get_tts_engine():
    """TTS engine'i al"""
    global tts_engine
    if tts_engine is None:
        print("🚀 TTS Engine başlatılıyor...")
        tts_engine = RecLastTTSEngine()
    return tts_engine


def get_voice_cloner():
    """Voice cloner'ı al"""
    global voice_cloner
    if voice_cloner is None:
        print("🎭 Voice Cloner başlatılıyor...")
        voice_cloner = VoiceCloner()
    return voice_cloner


# HTML Template (basit ve temiz)
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RecLastTTS - Ses Üretim Sistemi</title>
    <link rel="icon" type="image/x-icon" href="/assets/logo.ico">
    <link rel="shortcut icon" type="image/x-icon" href="/assets/logo.ico">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .content { padding: 30px; }
        .tabs { display: flex; border-bottom: 2px solid #eee; margin-bottom: 30px; }
        .tab { padding: 15px 25px; cursor: pointer; border: none; background: none; font-size: 16px; color: #666; border-bottom: 3px solid transparent; transition: all 0.3s; }
        .tab.active { color: #667eea; border-bottom-color: #667eea; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #333; }
        .form-group input, .form-group textarea, .form-group select { width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; transition: border-color 0.3s; }
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus { outline: none; border-color: #667eea; }
        .btn { background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 15px 30px; border-radius: 8px; font-size: 16px; cursor: pointer; transition: transform 0.2s; }
        .btn:hover { transform: translateY(-2px); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .btn-secondary { background: #6c757d; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 14px; }
        .btn-secondary:hover { background: #5a6268; }
        .clone-section { background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .clone-section h3 { margin-top: 0; color: #495057; }
        .status { margin-top: 20px; padding: 15px; border-radius: 8px; display: none; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .audio-player { margin-top: 20px; width: 100%; }
        .loading { display: none; text-align: center; margin: 20px 0; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 10px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .row { display: flex; gap: 20px; margin-bottom: 20px; }
        .col { flex: 1; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="/assets/logo.png" alt="RecLastTTS Logo" style="height: 80px; margin-bottom: 10px;">
            <h1>RecLastTTS</h1>
            <p>Ses Üretim Sistemi</p>
            <p style="font-size: 0.9em; margin-top: 5px;">Yerel • Ücretsiz • Sınırsız</p>
        </div>

        <div class="content">
            <div class="tabs">
                <button class="tab active" onclick="showTab('tts')">🎤 Text to Speech</button>
                <button class="tab" onclick="showTab('clone')">🎭 Ses Klonlama</button>
                <button class="tab" onclick="showTab('status')">📊 Durum</button>
            </div>

            <!-- TTS Tab -->
            <div id="tts" class="tab-content active">
                <h2>🎤 Text to Speech</h2>
                <form id="ttsForm">
                    <div class="form-group">
                        <label for="text">Metin:</label>
                        <textarea id="text" name="text" rows="4" placeholder="Buraya çevirmek istediğiniz metni yazın..." required></textarea>
                    </div>

                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label for="language">Dil:</label>
                                <select id="language" name="language">
                                    <option value="tr">Türkçe</option>
                                    <option value="en">English</option>
                                </select>
                            </div>
                        </div>

                        <div class="col">
                            <div class="form-group">
                                <label for="voice">Ses:</label>
                                <select id="voice" name="voice">
                                    <option value="">Varsayılan Ses</option>
                                </select>
                                <button type="button" id="refreshVoices" class="btn-secondary" style="margin-left: 10px;">🔄</button>
                            </div>
                        </div>

                        <div class="col">
                            <div class="form-group">
                                <label for="speed">Hız:</label>
                                <select id="speed" name="speed">
                                    <option value="0.5">0.5x (Yavaş)</option>
                                    <option value="0.8">0.8x</option>
                                    <option value="1.0" selected>1.0x (Normal)</option>
                                    <option value="1.2">1.2x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2.0">2.0x (Hızlı)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn">🎵 Ses Üret</button>
                </form>

                <div class="loading" id="ttsLoading">
                    <div class="spinner"></div>
                    <p>Ses üretiliyor...</p>
                </div>

                <div class="status" id="ttsStatus"></div>
                <audio class="audio-player" id="ttsAudio" controls style="display: none;"></audio>
            </div>

            <!-- Clone Tab -->
            <div id="clone" class="tab-content">
                <h2>🎭 Ses Klonlama</h2>

                <!-- Ses Oluşturma Formu -->
                <div class="clone-section">
                    <h3>🎯 Yeni Ses Oluştur</h3>
                    <form id="createVoiceForm" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="voiceName">Ses Adı:</label>
                            <input type="text" id="voiceName" name="voiceName" placeholder="Örn: Ümit" required>
                            <small>Bu isim ses listesinde görünecek</small>
                        </div>

                        <div class="form-group">
                            <label for="voiceDescription">Açıklama (İsteğe bağlı):</label>
                            <input type="text" id="voiceDescription" name="description" placeholder="Örn: Erkek ses, orta yaş">
                        </div>

                        <div class="form-group">
                            <label for="referenceAudio">Referans Ses Dosyası:</label>
                            <input type="file" id="referenceAudio" name="audio" accept="audio/*" required>
                            <small>WAV, MP3, FLAC formatları desteklenir. 3-10 saniye arası temiz ses kayıtları önerilir.</small>
                        </div>

                        <div class="form-group">
                            <label for="voiceLanguage">Dil:</label>
                            <select id="voiceLanguage" name="language">
                                <option value="tr">Türkçe</option>
                                <option value="en">English</option>
                            </select>
                        </div>

                        <button type="submit" class="btn">💾 Ses Oluştur</button>
                    </form>
                </div>

                <hr style="margin: 30px 0;">

                <!-- Hızlı Test Formu -->
                <div class="clone-section">
                    <h3>⚡ Hızlı Test (Geçici)</h3>
                    <form id="cloneForm" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="cloneText">Metin:</label>
                            <textarea id="cloneText" name="text" rows="3" placeholder="Klonlanmış sesle söylenecek metin..." required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="audioFile">Referans Ses Dosyası:</label>
                            <input type="file" id="audioFile" name="audio" accept="audio/*" required>
                            <small>Bu ses kaydedilmez, sadece test için kullanılır</small>
                        </div>

                        <div class="form-group">
                            <label for="cloneLanguage">Dil:</label>
                            <select id="cloneLanguage" name="language">
                                <option value="tr">Türkçe</option>
                                <option value="en">English</option>
                            </select>
                        </div>

                        <button type="submit" class="btn">🎭 Hızlı Test</button>
                    </form>
                </div>

                <div class="loading" id="cloneLoading">
                    <div class="spinner"></div>
                    <p>Ses klonlanıyor...</p>
                </div>

                <div class="status" id="cloneStatus"></div>
                <audio class="audio-player" id="cloneAudio" controls style="display: none;"></audio>
            </div>

            <!-- Status Tab -->
            <div id="status" class="tab-content">
                <h2>📊 Sistem Durumu</h2>
                <button class="btn" onclick="refreshStatus()">🔄 Yenile</button>
                <div id="systemStatus" style="margin-top: 20px;">
                    <p>Yüklemek için yenile butonuna tıklayın...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function showStatus(elementId, message, isError = false) {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = 'status ' + (isError ? 'error' : 'success');
            statusEl.style.display = 'block';
        }

        function hideStatus(elementId) { document.getElementById(elementId).style.display = 'none'; }
        function showLoading(elementId) { document.getElementById(elementId).style.display = 'block'; }
        function hideLoading(elementId) { document.getElementById(elementId).style.display = 'none'; }

        // TTS Form
        document.getElementById('ttsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);

            hideStatus('ttsStatus');
            showLoading('ttsLoading');
            document.getElementById('ttsAudio').style.display = 'none';

            try {
                const response = await fetch('/api/tts', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const audioUrl = URL.createObjectURL(blob);
                    const audioEl = document.getElementById('ttsAudio');
                    audioEl.src = audioUrl;
                    audioEl.style.display = 'block';
                    showStatus('ttsStatus', '✅ Ses başarıyla üretildi!');
                } else {
                    const error = await response.text();
                    showStatus('ttsStatus', '❌ Hata: ' + error, true);
                }
            } catch (error) {
                showStatus('ttsStatus', '❌ Bağlantı hatası: ' + error.message, true);
            } finally {
                hideLoading('ttsLoading');
            }
        });

        // Clone Form
        document.getElementById('cloneForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);

            hideStatus('cloneStatus');
            showLoading('cloneLoading');
            document.getElementById('cloneAudio').style.display = 'none';

            try {
                const response = await fetch('/api/clone', { method: 'POST', body: formData });

                if (response.ok) {
                    const blob = await response.blob();
                    const audioUrl = URL.createObjectURL(blob);
                    const audioEl = document.getElementById('cloneAudio');
                    audioEl.src = audioUrl;
                    audioEl.style.display = 'block';
                    showStatus('cloneStatus', '✅ Ses başarıyla klonlandı!');
                } else {
                    const error = await response.text();
                    showStatus('cloneStatus', '❌ Hata: ' + error, true);
                }
            } catch (error) {
                showStatus('cloneStatus', '❌ Bağlantı hatası: ' + error.message, true);
            } finally {
                hideLoading('cloneLoading');
            }
        });

        // Status refresh
        async function refreshStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();

                document.getElementById('systemStatus').innerHTML = `
                    <h3>🖥️ Sistem Bilgileri</h3>
                    <p><strong>Durum:</strong> ${data.status}</p>
                    <p><strong>Model:</strong> ${data.model}</p>
                    <p><strong>Device:</strong> ${data.device}</p>
                    <p><strong>GPU:</strong> ${data.gpu ? '✅ Aktif' : '❌ Pasif'}</p>
                    <p><strong>Diller:</strong> ${data.languages.join(', ')}</p>
                    <p><strong>Çalışma Süresi:</strong> ${Math.round(data.uptime)} saniye</p>
                `;
            } catch (error) {
                document.getElementById('systemStatus').innerHTML = `<p style="color: red;">❌ Durum bilgisi alınamadı: ${error.message}</p>`;
            }
        }

        // Ses listesini yükle
        async function loadVoices() {
            try {
                const response = await fetch('/api/voices');
                const data = await response.json();

                const voiceSelect = document.getElementById('voice');
                voiceSelect.innerHTML = '<option value="">Varsayılan Ses</option>';

                if (data.success && data.voices) {
                    data.voices.forEach(voice => {
                        const option = document.createElement('option');
                        option.value = voice.name;
                        option.textContent = `${voice.name} (${voice.language})`;
                        voiceSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Ses listesi yüklenemedi:', error);
            }
        }

        // Ses yenile butonu
        document.getElementById('refreshVoices').addEventListener('click', loadVoices);

        // TTS formunu güncelle
        document.getElementById('ttsForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = {
                text: formData.get('text'),
                language: formData.get('language'),
                speed: parseFloat(formData.get('speed')),
                voice: formData.get('voice')
            };

            const loading = document.getElementById('ttsLoading');
            const status = document.getElementById('ttsStatus');
            const audio = document.getElementById('ttsAudio');

            loading.style.display = 'block';
            status.style.display = 'none';
            audio.style.display = 'none';

            try {
                const response = await fetch('/api/tts', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = URL.createObjectURL(blob);
                    audio.src = url;
                    audio.style.display = 'block';
                    status.className = 'status success';
                    status.textContent = '✅ Ses başarıyla oluşturuldu!';
                } else {
                    const errorText = await response.text();
                    status.className = 'status error';
                    status.textContent = `❌ Hata: ${errorText}`;
                }
            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ Bağlantı hatası: ${error.message}`;
            } finally {
                loading.style.display = 'none';
                status.style.display = 'block';
            }
        });

        // Ses oluşturma formu
        document.getElementById('createVoiceForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const loading = document.getElementById('cloneLoading');
            const status = document.getElementById('cloneStatus');

            loading.style.display = 'block';
            status.style.display = 'none';

            try {
                const response = await fetch('/api/voices/create', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    status.className = 'status success';
                    status.textContent = `✅ ${data.message}`;
                    this.reset();
                    loadVoices(); // Ses listesini yenile
                } else {
                    status.className = 'status error';
                    status.textContent = `❌ Hata: ${data.error}`;
                }
            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ Bağlantı hatası: ${error.message}`;
            } finally {
                loading.style.display = 'none';
                status.style.display = 'block';
            }
        });

        window.addEventListener('load', function() {
            refreshStatus();
            loadVoices();
        });
    </script>
</body>
</html>
"""


def create_flask_app():
    """Flask uygulamasını oluştur"""
    app = Flask(__name__)

    @app.route('/')
    def index():
        return render_template_string(HTML_TEMPLATE)

    @app.route('/assets/<path:filename>')
    def assets_files(filename):
        """Assets dosyalar için route"""
        import os
        from flask import send_from_directory

        # Proje kök dizinini bul
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        assets_path = os.path.join(project_root, 'assets')

        # Assets klasöründe dosya var mı kontrol et
        asset_file = os.path.join(assets_path, filename)
        if os.path.exists(asset_file):
            return send_from_directory(assets_path, filename)

        return "Asset not found", 404

    @app.route('/api/tts', methods=['POST'])
    def api_tts():
        try:
            data = request.json
            text = data.get('text', '')
            language = data.get('language', 'tr')
            speed = float(data.get('speed', 1.0))
            voice = data.get('voice', '')

            if not text.strip():
                return "Metin boş olamaz", 400

            engine = get_tts_engine()

            # Ses klonu varsa dosyalarını al
            speaker_wav = None
            if voice:
                from ..core.voice_cloner import get_voice_cloner
                cloner = get_voice_cloner()
                speaker_files = cloner.get_voice_clone_files(voice)
                if speaker_files:
                    speaker_wav = speaker_files
                    cloner.update_voice_usage(voice)
                else:
                    return f"Ses bulunamadı: {voice}", 404

            audio_data = engine.text_to_speech(text=text, language=language, speed=speed, speaker_wav=speaker_wav)

            if audio_data is None:
                return "TTS işlemi başarısız", 500

            # Output klasörüne kaydet
            import time
            from ..core.config import get_config
            config = get_config()
            output_dir = config.get_output_path()
            os.makedirs(output_dir, exist_ok=True)

            timestamp = int(time.time())
            filename = f"tts_output_{timestamp}.wav"
            output_path = os.path.join(output_dir, filename)

            success = engine.save_audio(audio_data, output_path)
            if not success:
                return "Ses dosyası kaydedilemedi", 500

            return send_file(output_path, mimetype='audio/wav', as_attachment=True, download_name=filename)

        except Exception as e:
            return f"Hata: {str(e)}", 500

    @app.route('/api/clone', methods=['POST'])
    def api_clone():
        try:
            text = request.form.get('text', '')
            language = request.form.get('language', 'tr')
            audio_file = request.files.get('audio')

            if not text.strip():
                return "Metin boş olamaz", 400
            if not audio_file:
                return "Ses dosyası gerekli", 400

            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                audio_file.save(temp_audio.name)
                temp_audio_path = temp_audio.name

            try:
                engine = get_tts_engine()
                audio_data = engine.text_to_speech(text=text, language=language, speaker_wav=[temp_audio_path])

                if audio_data is None:
                    return "Ses klonlama başarısız", 500

                # Output klasörüne kaydet
                import time
                from ..core.config import get_config
                config = get_config()
                output_dir = config.get_output_path()
                os.makedirs(output_dir, exist_ok=True)

                timestamp = int(time.time())
                filename = f"cloned_voice_{timestamp}.wav"
                output_path = os.path.join(output_dir, filename)

                success = engine.save_audio(audio_data, output_path)
                if not success:
                    return "Ses dosyası kaydedilemedi", 500

                return send_file(output_path, mimetype='audio/wav', as_attachment=True, download_name=filename)

            finally:
                try:
                    os.unlink(temp_audio_path)
                except:
                    pass

        except Exception as e:
            return f"Hata: {str(e)}", 500

    @app.route('/api/voices', methods=['GET'])
    def api_voices():
        """Ses listesini al"""
        try:
            from ..core.voice_cloner import get_voice_cloner
            cloner = get_voice_cloner()
            voices = cloner.list_voice_clones()
            return jsonify({'success': True, 'voices': voices})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/voices/create', methods=['POST'])
    def api_create_voice():
        """Yeni ses oluştur"""
        try:
            voice_name = request.form.get('voiceName', '').strip()
            description = request.form.get('description', '').strip()
            language = request.form.get('language', 'tr')
            audio_file = request.files.get('audio')

            if not voice_name:
                return jsonify({'success': False, 'error': 'Ses adı gerekli'}), 400
            if not audio_file:
                return jsonify({'success': False, 'error': 'Ses dosyası gerekli'}), 400

            # Geçici dosya oluştur
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                audio_file.save(temp_audio.name)
                temp_audio_path = temp_audio.name

            try:
                from ..core.voice_cloner import get_voice_cloner
                cloner = get_voice_cloner()

                success = cloner.create_voice_clone(
                    voice_name=voice_name,
                    reference_audio_paths=[temp_audio_path],
                    description=description,
                    language=language
                )

                if success:
                    return jsonify({'success': True, 'message': f'Ses oluşturuldu: {voice_name}'})
                else:
                    return jsonify({'success': False, 'error': 'Ses oluşturulamadı'}), 500

            finally:
                try:
                    os.unlink(temp_audio_path)
                except:
                    pass

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/status')
    def api_status():
        try:
            engine = get_tts_engine()
            health = engine.health_check()

            return jsonify({
                'status': health.get('status', 'unknown'),
                'model': health.get('model_status', 'unknown'),
                'device': health.get('device', 'unknown'),
                'gpu': health.get('gpu_available', False),
                'languages': health.get('supported_languages', []),
                'uptime': time.time() - start_time
            })

        except Exception as e:
            return jsonify({'status': 'error', 'error': str(e)}), 500

    return app


def launch_ui(host: str = None, port: int = None, debug: bool = False):
    """UI'yi başlat"""
    config = get_config()
    logger = get_logger(__name__)

    if host is None:
        host = config.ui.host
    if port is None:
        port = config.ui.port

    logger.info(f"🌐 UI başlatılıyor: http://{host}:{port}")

    app = create_flask_app()

    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        logger.info("🛑 UI durduruldu (Ctrl+C)")
    except Exception as e:
        logger.error(f"❌ UI hatası: {e}")


def main():
    """CLI entry point"""
    import argparse

    parser = argparse.ArgumentParser(description="RecLastTTS Web UI")
    parser.add_argument("--host", default=None, help="Host adresi")
    parser.add_argument("--port", type=int, default=None, help="Port numarası")
    parser.add_argument("--debug", action="store_true", help="Debug modu")

    args = parser.parse_args()

    launch_ui(host=args.host, port=args.port, debug=args.debug)


if __name__ == "__main__":
    main()



