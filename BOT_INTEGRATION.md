# 🤖 Bot Entegrasyonu Kılavuzu

RecLastTTS'y<PERSON>, Telegram veya diğer botlarınızla nasıl entegre edeceğinizi öğrenin.

## 🚀 Hızlı Başlangıç

### 1. API Server'ı Başlatın
```bash
# Tek tuşla başlatma
start_api.bat

# Manuel başlatma
python run_api.py
```

### 2. API Endpoint'leri
- **Base URL**: `http://127.0.0.1:8000`
- **Dokümantasyon**: `http://127.0.0.1:8000/docs`

## 📡 API Kullanımı

### Basit TTS
```python
import requests

def text_to_speech(text, language="tr"):
    """Metni sese çevir"""
    url = "http://127.0.0.1:8000/api/v1/tts"
    
    data = {
        "text": text,
        "language": language
    }
    
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        # Ses dos<PERSON>ını kaydet
        with open("output.wav", "wb") as f:
            f.write(response.content)
        return "output.wav"
    else:
        print(f"Hata: {response.status_code}")
        return None

# Kullanım
audio_file = text_to_speech("Merhaba dünya!")
```

### Ses Klonlama
```python
import requests

def clone_voice(text, audio_file_path, language="tr"):
    """Ses klonlama ile TTS"""
    url = "http://127.0.0.1:8000/api/v1/clone"
    
    data = {
        "text": text,
        "language": language
    }
    
    files = {
        "audio": open(audio_file_path, "rb")
    }
    
    response = requests.post(url, data=data, files=files)
    
    if response.status_code == 200:
        with open("cloned_output.wav", "wb") as f:
            f.write(response.content)
        return "cloned_output.wav"
    else:
        print(f"Hata: {response.status_code}")
        return None

# Kullanım
audio_file = clone_voice("Bu benim sesim!", "reference_voice.wav")
```

## 🤖 Discord Bot Örneği

```python
import discord
from discord.ext import commands
import requests
import io

class TTSBot(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.tts_url = "http://127.0.0.1:8000/api/v1/tts"
    
    @commands.command(name="tts")
    async def text_to_speech(self, ctx, *, text):
        """TTS komutu: !tts Merhaba dünya"""
        
        if len(text) > 500:
            await ctx.send("❌ Metin çok uzun! (Max 500 karakter)")
            return
        
        try:
            # TTS isteği
            data = {"text": text, "language": "tr"}
            response = requests.post(self.tts_url, json=data, timeout=30)
            
            if response.status_code == 200:
                # Ses dosyasını Discord'a gönder
                audio_file = discord.File(
                    io.BytesIO(response.content), 
                    filename="tts_output.wav"
                )
                await ctx.send(f"🎵 **TTS**: {text[:50]}...", file=audio_file)
            else:
                await ctx.send(f"❌ TTS hatası: {response.status_code}")
                
        except Exception as e:
            await ctx.send(f"❌ Hata: {str(e)}")
    
    @commands.command(name="clone")
    async def voice_clone(self, ctx, *, text):
        """Ses klonlama: !clone Merhaba (+ ses dosyası ekle)"""
        
        if not ctx.message.attachments:
            await ctx.send("❌ Ses dosyası ekleyin!")
            return
        
        attachment = ctx.message.attachments[0]
        
        if not attachment.filename.lower().endswith(('.wav', '.mp3', '.flac')):
            await ctx.send("❌ Sadece ses dosyaları! (WAV, MP3, FLAC)")
            return
        
        try:
            # Ses dosyasını indir
            audio_data = await attachment.read()
            
            # Klonlama isteği
            files = {"audio": ("voice.wav", audio_data, "audio/wav")}
            data = {"text": text, "language": "tr"}
            
            response = requests.post(
                "http://127.0.0.1:8000/api/v1/clone", 
                data=data, 
                files=files, 
                timeout=60
            )
            
            if response.status_code == 200:
                audio_file = discord.File(
                    io.BytesIO(response.content), 
                    filename="cloned_voice.wav"
                )
                await ctx.send(f"🎭 **Klonlanmış Ses**: {text[:50]}...", file=audio_file)
            else:
                await ctx.send(f"❌ Klonlama hatası: {response.status_code}")
                
        except Exception as e:
            await ctx.send(f"❌ Hata: {str(e)}")

# Bot setup
bot = commands.Bot(command_prefix="!", intents=discord.Intents.all())

@bot.event
async def on_ready():
    print(f"✅ {bot.user} aktif!")

# Cog ekle
bot.add_cog(TTSBot(bot))

# Bot'u çalıştır
bot.run("YOUR_BOT_TOKEN")
```

## 📱 Telegram Bot Örneği

```python
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters
import requests
import io

class TTSTelegramBot:
    def __init__(self, token):
        self.app = Application.builder().token(token).build()
        self.tts_url = "http://127.0.0.1:8000/api/v1/tts"
        self.clone_url = "http://127.0.0.1:8000/api/v1/clone"
        
        # Komutları ekle
        self.app.add_handler(CommandHandler("tts", self.tts_command))
        self.app.add_handler(CommandHandler("start", self.start_command))
        self.app.add_handler(MessageHandler(filters.VOICE, self.voice_handler))
    
    async def start_command(self, update: Update, context):
        """Başlangıç komutu"""
        await update.message.reply_text(
            "🎙️ **RecLastTTS Bot**\n\n"
            "Komutlar:\n"
            "/tts <metin> - Metni sese çevir\n"
            "Ses mesajı gönder - Ses klonlama için"
        )
    
    async def tts_command(self, update: Update, context):
        """TTS komutu"""
        if not context.args:
            await update.message.reply_text("❌ Kullanım: /tts <metin>")
            return
        
        text = " ".join(context.args)
        
        if len(text) > 500:
            await update.message.reply_text("❌ Metin çok uzun! (Max 500 karakter)")
            return
        
        try:
            # TTS isteği
            data = {"text": text, "language": "tr"}
            response = requests.post(self.tts_url, json=data, timeout=30)
            
            if response.status_code == 200:
                # Ses dosyasını gönder
                audio_file = io.BytesIO(response.content)
                audio_file.name = "tts_output.wav"
                
                await update.message.reply_voice(
                    voice=audio_file,
                    caption=f"🎵 TTS: {text[:50]}..."
                )
            else:
                await update.message.reply_text(f"❌ TTS hatası: {response.status_code}")
                
        except Exception as e:
            await update.message.reply_text(f"❌ Hata: {str(e)}")
    
    async def voice_handler(self, update: Update, context):
        """Ses mesajı işleyici (klonlama için)"""
        await update.message.reply_text(
            "🎭 Ses klonlama için bu sesi kullanmak istediğiniz metni yazın:"
        )
        
        # Ses dosyasını kaydet (geçici)
        voice_file = await update.message.voice.get_file()
        # ... klonlama işlemi
    
    def run(self):
        """Bot'u çalıştır"""
        print("🤖 Telegram bot başlatılıyor...")
        self.app.run_polling()

# Bot'u çalıştır
if __name__ == "__main__":
    bot = TTSTelegramBot("YOUR_BOT_TOKEN")
    bot.run()
```

## 🔧 Performans İpuçları

### 1. Bağlantı Havuzu
```python
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Bağlantı havuzu oluştur
session = requests.Session()
retry_strategy = Retry(total=3, backoff_factor=1)
adapter = HTTPAdapter(max_retries=retry_strategy)
session.mount("http://", adapter)

# Kullanım
response = session.post(url, json=data)
```

### 2. Async İstekler
```python
import aiohttp
import asyncio

async def async_tts(text, language="tr"):
    """Async TTS isteği"""
    async with aiohttp.ClientSession() as session:
        data = {"text": text, "language": language}
        
        async with session.post(
            "http://127.0.0.1:8000/api/v1/tts", 
            json=data
        ) as response:
            if response.status == 200:
                return await response.read()
            return None

# Kullanım
audio_data = await async_tts("Merhaba!")
```

### 3. Önbellekleme
```python
import hashlib
import os

def cached_tts(text, language="tr", cache_dir="cache"):
    """Önbellekli TTS"""
    # Cache key oluştur
    cache_key = hashlib.md5(f"{text}_{language}".encode()).hexdigest()
    cache_file = os.path.join(cache_dir, f"{cache_key}.wav")
    
    # Cache'de var mı kontrol et
    if os.path.exists(cache_file):
        with open(cache_file, "rb") as f:
            return f.read()
    
    # TTS isteği yap
    response = requests.post(url, json={"text": text, "language": language})
    
    if response.status_code == 200:
        # Cache'e kaydet
        os.makedirs(cache_dir, exist_ok=True)
        with open(cache_file, "wb") as f:
            f.write(response.content)
        
        return response.content
    
    return None
```

## 🚨 Hata Yönetimi

```python
def safe_tts(text, language="tr", max_retries=3):
    """Güvenli TTS isteği"""
    for attempt in range(max_retries):
        try:
            response = requests.post(
                "http://127.0.0.1:8000/api/v1/tts",
                json={"text": text, "language": language},
                timeout=30
            )
            
            if response.status_code == 200:
                return response.content
            elif response.status_code == 500:
                print(f"Server hatası, tekrar deneniyor... ({attempt + 1}/{max_retries})")
                time.sleep(2)
            else:
                print(f"HTTP Hatası: {response.status_code}")
                break
                
        except requests.exceptions.Timeout:
            print(f"Timeout, tekrar deneniyor... ({attempt + 1}/{max_retries})")
            time.sleep(2)
        except requests.exceptions.ConnectionError:
            print("❌ API Server'a bağlanılamıyor! start_api.bat çalıştırın.")
            break
        except Exception as e:
            print(f"Beklenmeyen hata: {e}")
            break
    
    return None
```

## 📋 Checklist

Botunuzu entegre etmeden önce:

- [ ] RecLastTTS API Server çalışıyor (`start_api.bat`)
- [ ] API endpoint'leri test edildi (`http://127.0.0.1:8000/docs`)
- [ ] Bot token'ları ayarlandı
- [ ] Hata yönetimi eklendi
- [ ] Rate limiting düşünüldü
- [ ] Önbellekleme sistemi kuruldu (opsiyonel)

## 🎯 Sonuç

Bu kılavuzla RecLastTTS'yi herhangi bir bot platformuna entegre edebilirsiniz. API basit ve güçlü - sadece HTTP istekleri gönderin ve ses dosyalarını alın!

**Önemli**: API Server'ın sürekli çalışır durumda olması gerekir. Production ortamında systemd, PM2 veya Windows Service kullanın.
