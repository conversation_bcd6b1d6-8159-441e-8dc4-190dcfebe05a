@echo off
title RecLastTTS - Kurulum
echo.
echo ========================================
echo    RecLastTTS - Otomatik Kurulum
echo ========================================
echo.

REM Python kontrolü
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python bulunamadı!
    echo 📥 Python 3.8+ yükleyin: https://python.org
    pause
    exit /b 1
)

echo ✅ Python bulundu
echo.

REM Sanal ortam oluştur
if not exist "venv" (
    echo 📦 Sanal ortam oluşturuluyor...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Sanal ortam oluşturulamadı!
        pause
        exit /b 1
    )
    echo ✅ Sanal ortam oluşturuldu
) else (
    echo ✅ Sanal ortam mevcut
)

REM Sanal ortamı aktifleştir
echo 📦 Sanal ortam aktifleştiriliyor...
call venv\Scripts\activate.bat

REM Pip güncellemesi
echo 📦 Pip güncelleniyor...
python -m pip install --upgrade pip

REM Bağımlılıkları yükle
echo 📦 Bağımlılıklar yükleniyor...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ Bağımlılık yükleme hatası!
    echo 🔧 Manuel yükleme: pip install -r requirements.txt
    pause
    exit /b 1
)

REM Klasörleri oluştur
echo 📁 Klasörler oluşturuluyor...
if not exist "models" mkdir models
if not exist "output" mkdir output
if not exist "temp" mkdir temp
if not exist "logs" mkdir logs

echo.
echo ========================================
echo ✅ Kurulum tamamlandı!
echo ========================================
echo.
echo 🎯 Kullanım:
echo   • Web UI: start_ui.bat
echo   • API Server: start_api.bat
echo.
echo 🌐 Web UI: http://127.0.0.1:7860
echo 📚 API Docs: http://127.0.0.1:8000/docs
echo.
pause
