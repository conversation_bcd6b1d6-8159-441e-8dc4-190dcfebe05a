@echo off
chcp 65001 >nul
title RecLastTTS Ses Uret<PERSON> - Web UI
echo.
echo ========================================
echo   RecLastTTS Ses Uretim <PERSON>
echo ========================================
echo.
echo [1/3] Web UI baslatiliyor...
echo [2/3] Tarayicinizda acilacak: http://127.0.0.1:7860
echo [3/3] Durdurmak icin: Ctrl+C
echo.

REM Python sanal ortamini kontrol et
if exist "venv\Scripts\activate.bat" (
    echo [INFO] Sanal ortam aktiflestirildi
    call venv\Scripts\activate.bat
)

echo [INFO] Python modulleri yukleniyor...
echo [INFO] TTS motoru baslatiliyor...
echo [INFO] Bu islem 10-30 saniye surebilir...
echo.

REM UI'yi baslat
python run_ui.py --host 127.0.0.1 --port 7860

echo.
echo [DONE] UI durduruldu.
pause
