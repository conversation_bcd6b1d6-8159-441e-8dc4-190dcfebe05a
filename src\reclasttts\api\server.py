"""
RecLastTTS API Server
FastAPI tabanlı REST API server
"""

import os
import sys
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

from .endpoints import router
from ..core.config import get_config
from ..utils.logger import get_logger


# Global değişkenler
app_instance = None
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Uygulama yaşam döngüsü yöneticisi"""
    # Başlangıç
    logger.info("🚀 RecLastTTS API Server başlatılıyor...")
    
    try:
        # TTS Engine'i önceden yükle
        from .endpoints import get_tts_engine, get_voice_cloner
        
        logger.info("📥 TTS Engine önceden yükleniyor...")
        engine = get_tts_engine()
        
        if engine.is_loaded:
            logger.info("✅ TTS Engine hazır")
        else:
            logger.error("❌ TTS Engine yüklenemedi")
        
        logger.info("📥 Voice Cloner önceden yükleniyor...")
        cloner = get_voice_cloner()
        logger.info("✅ Voice Cloner hazır")
        
        logger.info("🎉 RecLastTTS API Server başlatıldı!")
        
    except Exception as e:
        logger.error(f"❌ Server başlatma hatası: {e}")
    
    yield
    
    # Kapanış
    logger.info("🛑 RecLastTTS API Server kapatılıyor...")
    
    try:
        # Kaynakları temizle
        from .endpoints import tts_engine
        if tts_engine:
            tts_engine.cleanup()
        
        logger.info("✅ Kaynaklar temizlendi")
        
    except Exception as e:
        logger.error(f"❌ Kapanış hatası: {e}")


def create_app() -> FastAPI:
    """FastAPI uygulaması oluştur"""
    global app_instance
    
    if app_instance is not None:
        return app_instance
    
    config = get_config()
    
    # FastAPI uygulaması
    app = FastAPI(
        title="RecLastTTS API",
        description="Yerel, ücretsiz, sınırsız ve performanslı TTS API",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Üretimde daha kısıtlayıcı olmalı
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Router'ları ekle
    app.include_router(router, prefix="/api/v1")
    
    # Ana sayfa
    @app.get("/")
    async def root():
        """Ana sayfa"""
        return {
            "message": "RecLastTTS API Server",
            "version": "1.0.0",
            "status": "running",
            "docs": "/docs",
            "health": "/api/v1/health"
        }
    
    # Global hata yakalayıcı
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        """Global hata yakalayıcı"""
        logger.error(f"Global hata: {exc}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": "İç server hatası",
                "details": str(exc) if config.logging.level == "DEBUG" else None
            }
        )
    
    # HTTP hata yakalayıcı
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc):
        """HTTP hata yakalayıcı"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": exc.detail,
                "status_code": exc.status_code
            }
        )
    
    app_instance = app
    return app


def run_server(
    host: str = None,
    port: int = None,
    workers: int = None,
    reload: bool = False,
    log_level: str = "info"
):
    """
    API server'ı çalıştır
    
    Args:
        host: Host adresi
        port: Port numarası
        workers: Worker sayısı
        reload: Auto-reload
        log_level: Log seviyesi
    """
    config = get_config()
    
    # Varsayılan değerler
    if host is None:
        host = config.server.host
    if port is None:
        port = config.server.port
    if workers is None:
        workers = config.server.workers
    
    logger.info(f"🌐 Server başlatılıyor: http://{host}:{port}")
    logger.info(f"📚 API Dokümantasyonu: http://{host}:{port}/docs")
    
    # Uvicorn konfigürasyonu
    uvicorn_config = {
        "app": "reclasttts.api.server:create_app",
        "factory": True,
        "host": host,
        "port": port,
        "log_level": log_level,
        "access_log": True,
        "reload": reload
    }
    
    # Production modunda workers kullan
    if not reload and workers > 1:
        uvicorn_config["workers"] = workers
    
    try:
        uvicorn.run(**uvicorn_config)
    except KeyboardInterrupt:
        logger.info("🛑 Server durduruldu (Ctrl+C)")
    except Exception as e:
        logger.error(f"❌ Server hatası: {e}")
        sys.exit(1)


def main():
    """CLI entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RecLastTTS API Server")
    parser.add_argument("--host", default=None, help="Host adresi")
    parser.add_argument("--port", type=int, default=None, help="Port numarası")
    parser.add_argument("--workers", type=int, default=None, help="Worker sayısı")
    parser.add_argument("--reload", action="store_true", help="Auto-reload")
    parser.add_argument("--log-level", default="info", help="Log seviyesi")
    
    args = parser.parse_args()
    
    run_server(
        host=args.host,
        port=args.port,
        workers=args.workers,
        reload=args.reload,
        log_level=args.log_level
    )


if __name__ == "__main__":
    main()
