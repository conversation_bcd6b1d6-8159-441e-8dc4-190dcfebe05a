#!/usr/bin/env python3
"""
RecLastTTS Ses Üretim <PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON>ts<PERSON>, sınırsız ve performanslı TTS çözümü
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

# Minimum Python sürümü kontrolü (TTS kütüphanesi 3.12'yi desteklemiyor)
MIN_PYTHON_VERSION = (3, 9)
MAX_PYTHON_VERSION = (3, 11)

def check_python_version():
    """Python sürümünü kontrol et"""
    current_version = sys.version_info[:2]
    
    if current_version < MIN_PYTHON_VERSION:
        print(f"❌ Python {MIN_PYTHON_VERSION[0]}.{MIN_PYTHON_VERSION[1]}+ gerekli. Mevcut: {current_version[0]}.{current_version[1]}")
        return False
    
    if current_version > MAX_PYTHON_VERSION:
        print(f"❌ Python {MAX_PYTHON_VERSION[0]}.{MAX_PYTHON_VERSION[1]} veya altı gerekli (TTS kütüphanesi sınırlaması). Mevcut: {current_version[0]}.{current_version[1]}")
        print("💡 Python 3.11 kurmanız öneriliyor.")
        return False

    print(f"✅ Python sürümü uygun: {current_version[0]}.{current_version[1]}")
    return True

def check_system_requirements():
    """Sistem gereksinimlerini kontrol et"""
    print("🔍 Sistem gereksinimleri kontrol ediliyor...")
    
    # İşletim sistemi
    os_name = platform.system()
    if os_name != "Windows":
        print(f"⚠️  Bu sistem Windows için optimize edilmiş. Mevcut: {os_name}")
    else:
        print(f"✅ İşletim sistemi: {os_name}")
    
    # RAM kontrolü
    try:
        import psutil
        ram_gb = psutil.virtual_memory().total / (1024**3)
        if ram_gb < 16:
            print(f"⚠️  16GB+ RAM önerilen. Mevcut: {ram_gb:.1f}GB")
        else:
            print(f"✅ RAM: {ram_gb:.1f}GB")
    except ImportError:
        print("⚠️  RAM kontrolü yapılamadı (psutil gerekli)")
    
    return True

def check_gpu():
    """GPU ve CUDA kontrolü"""
    print("🎮 GPU ve CUDA kontrol ediliyor...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            cuda_version = torch.version.cuda
            print(f"✅ GPU bulundu: {gpu_name}")
            print(f"✅ CUDA sürümü: {cuda_version}")
            print(f"✅ GPU sayısı: {gpu_count}")
            
            # VRAM kontrolü
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            if gpu_memory < 8:
                print(f"⚠️  8GB+ VRAM önerilen. Mevcut: {gpu_memory:.1f}GB")
            else:
                print(f"✅ VRAM: {gpu_memory:.1f}GB")
            
            return True
        else:
            print("❌ CUDA GPU bulunamadı")
            return False
    except ImportError:
        print("⚠️  PyTorch henüz yüklenmemiş, GPU kontrolü yapılamadı")
        return None

def install_pytorch():
    """PyTorch ve CUDA desteğini yükle"""
    print("🔥 PyTorch ve CUDA desteği yükleniyor...")
    
    # RTX 4060 için CUDA 12.x önerilen
    pytorch_cmd = [
        sys.executable, "-m", "pip", "install", 
        "torch", "torchvision", "torchaudio", 
        "--index-url", "https://download.pytorch.org/whl/cu121"
    ]
    
    try:
        subprocess.run(pytorch_cmd, check=True)
        print("✅ PyTorch CUDA desteği yüklendi")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyTorch yüklenemedi: {e}")
        return False

def install_requirements():
    """Gerekli paketleri yükle"""
    print("📦 Gerekli paketler yükleniyor...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt bulunamadı")
        return False
    
    cmd = [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ Gerekli paketler yüklendi")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Paket yüklemesi başarısız: {e}")
        return False

def download_models():
    """XTTS v2 modelini indir"""
    print("🤖 XTTS v2 modeli indiriliyor...")
    
    try:
        from TTS.api import TTS
        
        # XTTS v2 modelini yükle (otomatik indirir)
        print("📥 XTTS v2 modeli indiriliyor... (Bu işlem biraz zaman alabilir)")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2")
        
        print("✅ XTTS v2 modeli başarıyla indirildi")
        return True
        
    except Exception as e:
        print(f"❌ Model indirme hatası: {e}")
        return False

def create_directories():
    """Gerekli klasörleri oluştur"""
    print("📁 Proje klasörleri oluşturuluyor...")
    
    directories = [
        "models",
        "models/xtts_v2", 
        "models/voice_clones",
        "src",
        "src/reclasttts",
        "src/reclasttts/api",
        "src/reclasttts/ui", 
        "src/reclasttts/core",
        "src/reclasttts/utils",
        "config",
        "scripts",
        "tests",
        "docs",
        "logs",
        "temp",
        "output"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Klasörler oluşturuldu")
    return True

def create_config():
    """Varsayılan konfigürasyon dosyası oluştur"""
    print("⚙️ Konfigürasyon dosyası oluşturuluyor...")
    
    config_content = """# RecLastTTS Konfigürasyonu
model:
  name: "xtts_v2"
  language_support: ["tr", "en"]
  max_text_length: 500
  device: "cuda"  # cuda veya cpu
  
audio:
  sample_rate: 22050
  format: "wav"
  quality: "high"
  
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  
ui:
  host: "0.0.0.0"
  port: 7860
  share: false
  
gpu:
  enabled: true
  memory_fraction: 0.8
  
logging:
  level: "INFO"
  file: "logs/reclasttts.log"
  
paths:
  models: "models"
  voice_clones: "models/voice_clones"
  temp: "temp"
  output: "output"
"""
    
    config_path = Path("config/settings.yaml")
    config_path.parent.mkdir(exist_ok=True)
    
    with open(config_path, "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("✅ Konfigürasyon dosyası oluşturuldu")
    return True

def test_installation():
    """Kurulumu test et"""
    print("🧪 Kurulum test ediliyor...")
    
    try:
        # PyTorch testi
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        # CUDA testi
        if torch.cuda.is_available():
            print(f"✅ CUDA: {torch.version.cuda}")
        else:
            print("⚠️  CUDA kullanılamıyor")
        
        # TTS testi
        from TTS.api import TTS
        print("✅ TTS kütüphanesi yüklü")
        
        # Basit TTS testi
        print("🎤 Basit TTS testi yapılıyor...")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2")
        
        # Test metni
        test_text = "RecLastTTS kurulumu başarılı!"
        
        # Ses üretimi testi (dosyaya kaydetmeden)
        wav = tts.tts(text=test_text, language="tr")
        
        if wav is not None and len(wav) > 0:
            print("✅ TTS testi başarılı!")
            return True
        else:
            print("❌ TTS testi başarısız")
            return False
            
    except Exception as e:
        print(f"❌ Test hatası: {e}")
        return False

def main():
    """Ana kurulum fonksiyonu"""
    print("🎙️ RecLastTTS Ses Üretim Sistemi Kurulumu")
    print("=" * 50)
    
    # Python sürüm kontrolü
    if not check_python_version():
        sys.exit(1)
    
    # Sistem gereksinimleri
    check_system_requirements()
    
    # Klasör oluşturma
    if not create_directories():
        sys.exit(1)
    
    # PyTorch kurulumu
    if not install_pytorch():
        print("⚠️  PyTorch kurulumu başarısız, devam ediliyor...")
    
    # Gereksinimler kurulumu
    if not install_requirements():
        sys.exit(1)
    
    # GPU kontrolü
    gpu_status = check_gpu()
    if gpu_status is False:
        print("⚠️  GPU bulunamadı, CPU modunda çalışacak")
    
    # Model indirme
    if not download_models():
        print("⚠️  Model indirme başarısız, manuel indirme gerekebilir")
    
    # Konfigürasyon oluşturma
    if not create_config():
        sys.exit(1)
    
    # Kurulum testi
    if test_installation():
        print("\n🎉 Kurulum başarıyla tamamlandı!")
        print("\n📋 Sonraki adımlar:")
        print("1. Web arayüzünü başlatmak için: python -m reclasttts.ui")
        print("2. API server'ı başlatmak için: python -m reclasttts.api")
        print("3. Dokümantasyon için: README.md dosyasını inceleyin")
        print("\n🔗 Entegrasyon için: INTEGRATION_GUIDE.md dosyasını inceleyin")
    else:
        print("\n❌ Kurulum tamamlandı ancak testler başarısız")
        print("Manuel kontrol gerekebilir")

if __name__ == "__main__":
    main()
