#!/usr/bin/env python3
"""
RecLastTTS Model İndirme Scripti
XTTS v2 modelini yerel olarak indirir ve GPU için optimize eder
"""

import os
import sys
import shutil
import time
from pathlib import Path

# Proje yolunu ekle
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from reclasttts.utils.logger import get_logger
from reclasttts.core.config import get_config


def check_gpu_status():
    """GPU durumunu kontrol et"""
    logger = get_logger(__name__)
    
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            cuda_version = torch.version.cuda
            
            logger.info(f"🎮 GPU Bilgileri:")
            logger.info(f"  - GPU: {gpu_name}")
            logger.info(f"  - VRAM: {gpu_memory:.1f}GB")
            logger.info(f"  - CUDA: {cuda_version}")
            logger.info(f"  - GPU Sayısı: {gpu_count}")
            
            return True, gpu_memory
        else:
            logger.warning("❌ CUDA GPU bulunamadı")
            return False, 0
            
    except Exception as e:
        logger.error(f"GPU kontrol hatası: {e}")
        return False, 0


def download_xtts_model():
    """XTTS v2 modelini indir"""
    logger = get_logger(__name__)
    config = get_config()
    
    try:
        logger.info("📥 XTTS v2 modeli indiriliyor...")
        
        from TTS.api import TTS
        
        # Model adı
        model_name = "tts_models/multilingual/multi-dataset/xtts_v2"
        
        # TTS API ile modeli yükle (otomatik indirir)
        start_time = time.time()
        tts = TTS(model_name)
        download_time = time.time() - start_time
        
        logger.info(f"✅ Model indirildi ({download_time:.1f}s)")
        
        # Model dosyalarının konumunu bul
        model_path = tts.model_path if hasattr(tts, 'model_path') else None
        
        if model_path:
            logger.info(f"📁 Model konumu: {model_path}")
        
        # GPU'ya yükle ve test et
        gpu_available, gpu_memory = check_gpu_status()

        if gpu_available:
            import torch
            logger.info("🔥 Model GPU'ya yükleniyor...")
            start_time = time.time()
            tts = tts.to("cuda")
            gpu_load_time = time.time() - start_time
            logger.info(f"✅ Model GPU'ya yüklendi ({gpu_load_time:.1f}s)")

            # GPU bellek kullanımını kontrol et
            if torch.cuda.is_available():
                allocated = torch.cuda.memory_allocated(0) / (1024**3)
                cached = torch.cuda.memory_reserved(0) / (1024**3)
                logger.info(f"📊 GPU Bellek: {allocated:.1f}GB allocated, {cached:.1f}GB cached")
        
        return tts, model_path
        
    except Exception as e:
        logger.error(f"❌ Model indirme hatası: {e}")
        return None, None


def copy_model_to_local():
    """Modeli yerel models klasörüne kopyala"""
    logger = get_logger(__name__)
    config = get_config()
    
    try:
        # Hedef klasör
        local_models_dir = config.get_model_path()
        os.makedirs(local_models_dir, exist_ok=True)
        
        logger.info(f"📁 Yerel model klasörü: {local_models_dir}")
        
        # TTS cache klasörünü bul
        from TTS.utils.manage import ModelManager
        manager = ModelManager()
        
        # Model dosyalarını bul
        model_name = "tts_models/multilingual/multi-dataset/xtts_v2"
        model_path = manager.download_model(model_name)
        
        if model_path and os.path.exists(model_path):
            logger.info(f"📂 Kaynak model: {model_path}")
            
            # Model dosyalarını kopyala
            if os.path.isdir(model_path):
                # Klasör ise içeriği kopyala
                for item in os.listdir(model_path):
                    src = os.path.join(model_path, item)
                    dst = os.path.join(local_models_dir, item)
                    
                    if os.path.isfile(src):
                        shutil.copy2(src, dst)
                        logger.info(f"📄 Kopyalandı: {item}")
                    elif os.path.isdir(src):
                        shutil.copytree(src, dst, dirs_exist_ok=True)
                        logger.info(f"📁 Kopyalandı: {item}/")
            else:
                # Dosya ise direkt kopyala
                dst = os.path.join(local_models_dir, os.path.basename(model_path))
                shutil.copy2(model_path, dst)
                logger.info(f"📄 Kopyalandı: {os.path.basename(model_path)}")
            
            logger.info(f"✅ Model yerel klasöre kopyalandı: {local_models_dir}")
            return True
        else:
            logger.error("❌ Model dosyaları bulunamadı")
            return False
            
    except Exception as e:
        logger.error(f"❌ Model kopyalama hatası: {e}")
        return False


def test_model_performance():
    """Model performansını test et"""
    logger = get_logger(__name__)
    
    try:
        logger.info("🧪 Model performans testi...")
        
        from reclasttts.core.tts_engine import RecLastTTSEngine
        
        # Engine'i başlat
        start_time = time.time()
        engine = RecLastTTSEngine()
        init_time = time.time() - start_time
        
        logger.info(f"⚡ Engine başlatma: {init_time:.1f}s")
        
        # Test metinleri
        test_texts = [
            "Kısa test",
            "Bu orta uzunlukta bir test metnidir.",
            "Bu uzun bir test metnidir. RecLastTTS sisteminin performansını ölçmek için kullanılıyor. Türkçe karakterler: ğüşıöç."
        ]
        
        total_chars = 0
        total_time = 0
        
        for i, text in enumerate(test_texts, 1):
            logger.info(f"🎤 Test {i}: {len(text)} karakter")
            
            start_time = time.time()
            audio_data = engine.text_to_speech(text, language="tr")
            tts_time = time.time() - start_time
            
            if audio_data is not None:
                chars_per_sec = len(text) / tts_time
                audio_duration = len(audio_data) / 22050  # 22kHz sample rate
                realtime_factor = audio_duration / tts_time
                
                logger.info(f"  ✅ Başarılı: {tts_time:.2f}s, {chars_per_sec:.1f} kar/s, RTF: {realtime_factor:.2f}x")
                
                total_chars += len(text)
                total_time += tts_time
            else:
                logger.error(f"  ❌ Başarısız")
        
        if total_time > 0:
            avg_chars_per_sec = total_chars / total_time
            logger.info(f"📊 Ortalama performans: {avg_chars_per_sec:.1f} karakter/saniye")
        
        # GPU bellek kullanımı
        import torch
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated(0) / (1024**3)
            cached = torch.cuda.memory_reserved(0) / (1024**3)
            logger.info(f"📊 GPU Bellek kullanımı: {allocated:.1f}GB / {cached:.1f}GB")
        
        engine.cleanup()
        return True
        
    except Exception as e:
        logger.error(f"❌ Performans testi hatası: {e}")
        return False


def optimize_gpu_settings():
    """GPU ayarlarını optimize et"""
    logger = get_logger(__name__)
    
    try:
        import torch
        
        if not torch.cuda.is_available():
            logger.warning("GPU bulunamadı, optimizasyon atlanıyor")
            return False
        
        logger.info("⚙️ GPU optimizasyonu yapılıyor...")
        
        # CUDA ayarları
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        
        # Bellek optimizasyonu
        torch.cuda.empty_cache()
        
        # GPU bellek fraksiyonunu ayarla
        config = get_config()
        memory_fraction = config.gpu.memory_fraction
        
        logger.info(f"🔧 GPU bellek fraksiyonu: {memory_fraction}")
        
        # CUDA bellek ayarları
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
        
        logger.info("✅ GPU optimizasyonu tamamlandı")
        return True
        
    except Exception as e:
        logger.error(f"❌ GPU optimizasyon hatası: {e}")
        return False


def main():
    """Ana fonksiyon"""
    logger = get_logger(__name__)
    
    logger.info("🎙️ RecLastTTS Model İndirme ve Optimizasyon")
    logger.info("=" * 50)
    
    # GPU durumu kontrol
    gpu_available, gpu_memory = check_gpu_status()
    
    if not gpu_available:
        logger.warning("⚠️ GPU bulunamadı, CPU modunda çalışacak")
    elif gpu_memory < 6:
        logger.warning(f"⚠️ GPU bellek düşük ({gpu_memory:.1f}GB), performans etkilenebilir")
    
    # GPU optimizasyonu
    optimize_gpu_settings()
    
    # Model indirme
    logger.info("\n📥 Model indirme işlemi başlıyor...")
    tts_model, model_path = download_xtts_model()
    
    if tts_model is None:
        logger.error("❌ Model indirilemedi")
        return False
    
    # Modeli yerel klasöre kopyala
    logger.info("\n📁 Model yerel klasöre kopyalanıyor...")
    copy_success = copy_model_to_local()
    
    if not copy_success:
        logger.warning("⚠️ Model kopyalanamadı, cache'den çalışacak")
    
    # Performans testi
    logger.info("\n🧪 Performans testi yapılıyor...")
    test_success = test_model_performance()
    
    if test_success:
        logger.info("\n🎉 Model indirme ve optimizasyon tamamlandı!")
        logger.info("✅ RecLastTTS kullanıma hazır")
        return True
    else:
        logger.error("\n❌ Performans testi başarısız")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
