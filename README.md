# 🎙️ RecLastTTS Ses Üretim Si<PERSON>

**<PERSON><PERSON>, Ücretsiz, Sınırs<PERSON>z ve Performanslı Text-to-Speech Çözümü**

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Python](https://img.shields.io/badge/python-3.11-green.svg)
![License](https://img.shields.io/badge/license-MIT-yellow.svg)
![Platform](https://img.shields.io/badge/platform-Windows-lightgrey.svg)

## 📋 Özellikler

- 🆓 **Tamamen Ücretsiz** - Açık kaynak ve sınırsız kullanım
- 🏠 **<PERSON><PERSON>alışma** - İnternet bağlantısı gerektirmez
- 🎭 **Ses Klonlama** - 6-10 saniye ses örneği ile klonlama
- 🎨 **Duygu & Mimik** - Doğal ve ifadeli ses üretimi
- 🌍 **Çok Dilli** - Türkçe ve İngilizce desteği
- ⚡ **Yüksek Performans** - RTX 4060 optimizasyonu
- 🔌 **API Desteği** - Diğer uygulamalarla kolay entegrasyon
- 📦 **Portable** - Model dosyaları dahil, taşınabilir yapı

## 🚀 Hızlı Başlangıç

### Sistem Gereksinimleri

- **İşlemci:** AMD Ryzen 7840 veya üzeri
- **RAM:** 16GB+ (32GB önerilen)
- **GPU:** NVIDIA RTX 4060 (8GB VRAM)
- **İşletim Sistemi:** Windows 10/11
- **Python:** 3.11
- **CUDA:** 12.x

### Otomatik Kurulum (Windows)

```bash
# 1. Repository'yi indirin
git clone https://github.com/yourusername/RecLastTTS.git
cd RecLastTTS

# 2. Tek tuşla kurulum
install.bat
```

### Tek Tuşla Çalıştırma

```bash
# Web UI (Tarayıcı arayüzü)
start_ui.bat

# API Server (Bot entegrasyonu için)
start_api.bat
```

### Manuel Kurulum

```bash
# Sanal ortam oluşturun
python -m venv venv
venv\Scripts\activate

# Bağımlılıkları yükleyin
pip install -r requirements.txt
```

### İlk Kullanım

```bash
# Web UI'yi başlatın
python run_ui.py
# Tarayıcıda: http://127.0.0.1:7860

# API Server'ı başlatın
python run_api.py
# API Docs: http://127.0.0.1:8000/docs
```

### Bot Entegrasyonu

Detaylı bot entegrasyonu için: [BOT_INTEGRATION.md](BOT_INTEGRATION.md)

```python
import requests

# Basit TTS
response = requests.post("http://127.0.0.1:8000/api/v1/tts", json={
    "text": "Merhaba dünya!",
    "language": "tr"
})

with open("output.wav", "wb") as f:
    f.write(response.content)
```

## 🔧 API Kullanımı

### REST API Server Başlatma

```bash
python -m reclasttts.api --host 0.0.0.0 --port 8000
```

### API Endpoints

#### Text-to-Speech
```bash
curl -X POST "http://localhost:8000/api/v1/tts" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "Merhaba dünya!",
       "language": "tr",
       "voice": "default"
     }' \
     --output output.wav
```

#### Ses Klonlama
```bash
curl -X POST "http://localhost:8000/api/v1/clone" \
     -F "audio=@reference.wav" \
     -F "text=Klonlanmış sesle konuşma" \
     -F "language=tr" \
     --output cloned.wav
```

#### Mevcut Sesler
```bash
curl "http://localhost:8000/api/v1/voices"
```

## 🎛️ Web Arayüzü

Web arayüzüne erişim için:

```bash
python -m reclasttts.ui
```

Tarayıcınızda `http://localhost:7860` adresini açın.

## 🔗 Diğer Uygulamalarla Entegrasyon

### Python SDK

```python
import requests

def generate_speech(text, language="tr"):
    response = requests.post(
        "http://localhost:8000/api/v1/tts",
        json={"text": text, "language": language}
    )
    return response.content

# Kullanım
audio_data = generate_speech("Test metni")
with open("output.wav", "wb") as f:
    f.write(audio_data)
```

### Video Üretim Botu Entegrasyonu

```python
class VideoBot:
    def __init__(self):
        self.tts_url = "http://localhost:8000/api/v1/tts"
    
    def add_voiceover(self, script, output_path):
        response = requests.post(
            self.tts_url,
            json={"text": script, "language": "tr"}
        )
        
        with open(output_path, "wb") as f:
            f.write(response.content)
        
        return output_path
```

## 📁 Proje Yapısı

```
RecLastTTS/
├── assets/                 # Logo ve ikonlar
├── models/                 # TTS modelleri
│   ├── xtts_v2/           # XTTS v2 modeli
│   └── voice_clones/      # Klonlanmış sesler
├── src/reclasttts/        # Ana kaynak kod
│   ├── api/               # REST API
│   ├── ui/                # Web arayüzü
│   ├── core/              # TTS motoru
│   └── utils/             # Yardımcı fonksiyonlar
├── config/                # Konfigürasyon dosyaları
├── scripts/               # Kurulum scriptleri
├── tests/                 # Test dosyaları
├── docs/                  # Dokümantasyon
├── requirements.txt       # Python bağımlılıkları
├── setup.py              # Kurulum scripti
├── README.md             # Bu dosya
└── PLAN.md               # Proje planı
```

## ⚙️ Konfigürasyon

### config/settings.yaml

```yaml
model:
  name: "xtts_v2"
  language_support: ["tr", "en"]
  max_text_length: 500
  
audio:
  sample_rate: 22050
  format: "wav"
  quality: "high"

server:
  host: "0.0.0.0"
  port: 8000
  workers: 1

gpu:
  enabled: true
  memory_fraction: 0.8
```

## 🧪 Test Etme

```bash
# Tüm testleri çalıştır
python -m pytest tests/

# Sadece API testleri
python -m pytest tests/test_api.py

# Performance testleri
python -m pytest tests/test_performance.py
```

## 📊 Performans

| Özellik | Değer |
|---------|-------|
| Ses Üretim Hızı | ~2x gerçek zamandan hızlı |
| GPU Bellek Kullanımı | ~4GB (RTX 4060) |
| CPU Kullanımı | %20-30 |
| Ses Kalitesi | 22kHz, 16-bit |
| Klonlama Süresi | ~30 saniye |

## 🔧 Sorun Giderme

### Yaygın Sorunlar

**CUDA Hatası:**
```bash
# CUDA kurulumunu kontrol edin
nvidia-smi
python -c "import torch; print(torch.cuda.is_available())"
```

**Bellek Hatası:**
```bash
# GPU bellek kullanımını azaltın
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
```

**Model İndirme Hatası:**
```bash
# Manuel model indirme
python -c "from TTS.api import TTS; TTS('tts_models/multilingual/multi-dataset/xtts_v2')"
```

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için [LICENSE](LICENSE) dosyasına bakın.

## 🙏 Teşekkürler

- [Coqui AI](https://github.com/coqui-ai/TTS) - XTTS v2 modeli için
- [PyTorch](https://pytorch.org/) - Deep learning framework
- [Gradio](https://gradio.app/) - Web arayüzü için

## 📞 İletişim

- **Proje Sahibi:** [Your Name]
- **Email:** <EMAIL>
- **GitHub:** [@yourusername](https://github.com/yourusername)

---

⭐ Bu projeyi beğendiyseniz yıldız vermeyi unutmayın!
